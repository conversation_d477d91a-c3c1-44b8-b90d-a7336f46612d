import React from 'react';
import { Award, Calendar, FileText, Building2, Brain, FlaskRound as Flask, Globe, ChevronRight, ExternalLink, Mail } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function PacificRim() {
  const timeline = [
    { days: 30, title: "Initial Setup", items: [
      "Memorandum of Understanding (MOU) reviewed and signed",
      "Process flow developed",
      "Data Use Agreement (DUA) verified",
      "Public Health Laboratories Identified"
    ]},
    { days: 60, title: "Implementation", items: [
      "Labs briefed and trained",
      "Proof of Concept begins",
      "DUA confirmed",
      "Model finalized",
      "Evaluation structure set"
    ]},
    { days: 90, title: "Evaluation", items: [
      "Evaluation begins",
      "Feedback monitored",
      "Scaling discussed",
      "Revisions made",
      "Go/No Go determined",
      "SOAR API completed"
    ]}
  ];

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-orange-500 via-orange-600 to-orange-700 text-white py-32 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated background elements */}
          <div className="absolute -top-1/2 -left-1/2 w-full h-full rounded-full bg-gradient-to-br from-orange-300/30 to-transparent blur-3xl animate-[spin_30s_linear_infinite]"></div>
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full rounded-full bg-gradient-to-br from-orange-400/30 to-transparent blur-3xl animate-[spin_25s_linear_infinite_reverse]"></div>
          
          {/* Animated grid pattern */}
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiIGlkPSJhIj48c3RvcCBzdG9wLWNvbG9yPSIjZmZmIiBzdG9wLW9wYWNpdHk9Ii4yIiBvZmZzZXQ9IjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iI2ZmZiIgc3RvcC1vcGFjaXR5PSIwIiBvZmZzZXQ9IjEwMCUiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cGF0aCBkPSJNMCAwaDYwdjYwSDB6IiBmaWxsPSJ1cmwoI2EpIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=')] opacity-20"></div>
          
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/80 via-orange-600/80 to-orange-700/80 backdrop-blur-[2px]"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="animate-[fadeIn_1s_ease-out]">
              <Award className="h-16 w-16 text-white mb-6" />
              <h1 className="text-4xl font-bold mb-6">
                Pacific Rim Forecasting Initiative
              </h1>
              <p className="text-xl text-orange-100 mb-8">
                Scalable Metagenomic Surveillance in Public Health Laboratories
              </p>
              <a
                href="https://www.cdc.gov/forecast-outbreak-analytics/partners/insightnet/index.html"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-white text-orange-600 px-6 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5"
              >
                Learn More About Insight Net
                <ExternalLink className="ml-2 h-5 w-5" />
              </a>
            </div>
            <div className="relative animate-[slideIn_1s_ease-out]">
              <img
                src="https://images.unsplash.com/photo-1576086213369-97a306d36557?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                alt="Laboratory Analysis"
                className="rounded-lg shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Background Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Background</h2>
              <p className="text-gray-600 mb-6">
                On 09/12/2023, International Responder Systems was one of thirteen recipients awarded a five-year cooperative agreement from the Centers for Disease Control Center for Forecasting and Analytics to implement disease forecasting models nationwide.
              </p>
              <p className="text-gray-600">
                International Responder Systems is working on a proof-of-concept approach to nationwide implementation by first working with the Pacific Rim Consortium state laboratories and Health Officials. This voluntary initiative seeks to establish a regional proof of concept for modeling and analytics, with the ultimate goal of nationwide scalability for Public Health Laboratories.
              </p>
            </div>
            <div>
              <img
                src="https://images.unsplash.com/photo-1581093588401-fbb62a02f120?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                alt="Public Health Laboratory"
                className="rounded-lg shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-center">Project Timeline</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {timeline.map((phase, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                <div className="flex items-center mb-4">
                  <Calendar className="h-8 w-8 text-orange-600 mr-3" />
                  <div>
                    <span className="text-2xl font-bold text-orange-600">{phase.days}</span>
                    <span className="text-gray-600 ml-1">Days</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4">{phase.title}</h3>
                <ul className="space-y-2">
                  {phase.items.map((item, i) => (
                    <li key={i} className="flex items-start">
                      <ChevronRight className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-600">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Features */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Key Components</h2>
            <p className="text-xl text-gray-600">Comprehensive solutions for public health surveillance</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-50 p-8 rounded-xl">
              <Brain className="h-12 w-12 text-orange-600 mb-6" />
              <h3 className="text-xl font-bold mb-4">SwabSeq Platform</h3>
              <p className="text-gray-600">
                UCLA's SwabSeq Metagenomic Diagnostic Platform offers comprehensive pathogen and variant identification through next-generation sequencing.
              </p>
              <a
                href="https://swabseq.compmed.ucla.edu/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-orange-600 mt-4 hover:text-orange-700"
              >
                Learn More
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </div>

            <div className="bg-gray-50 p-8 rounded-xl">
              <Globe className="h-12 w-12 text-orange-600 mb-6" />
              <h3 className="text-xl font-bold mb-4">SOAR Platform</h3>
              <p className="text-gray-600">
                Enabling both nowcasting and forecasting capabilities with comprehensive repository for reference documents and standards.
              </p>
            </div>

            <div className="bg-gray-50 p-8 rounded-xl">
              <Flask className="h-12 w-12 text-orange-600 mb-6" />
              <h3 className="text-xl font-bold mb-4">Laboratory Integration</h3>
              <p className="text-gray-600">
                Cloud-based ETOR platform for seamless test ordering, progress tracking, and results viewing.
              </p>
              <a
                href="https://www.iconnectconsulting.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-orange-600 mt-4 hover:text-orange-700"
              >
                Learn More
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-orange-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-8">Want to get involved & stay connected?</h2>
          <div className="max-w-2xl mx-auto">
            <div className="bg-white/10 backdrop-blur-sm p-8 rounded-lg">
              <h3 className="text-xl font-bold mb-2">Holli Dowless Kozar</h3>
              <p className="text-orange-100 mb-6">Principal Investigator</p>
              <Link
                to={"/contact"}
                className="inline-flex items-center bg-white text-orange-600 px-6 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-all duration-300"
              >
                <Mail className="mr-2 h-5 w-5" />
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}