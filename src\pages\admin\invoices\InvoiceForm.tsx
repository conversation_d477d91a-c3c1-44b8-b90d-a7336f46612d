import React, { useState, useEffect } from 'react';
import ReactD<PERSON> from 'react-dom';
import { supabase } from '../../../lib/supabase';
import { X, Plus, Trash2, Save, Search, UserPlus, Loader2 } from 'lucide-react';
import { Combobox } from '@headlessui/react';

interface InvoiceFormProps {
    isAdding: boolean;
    setIsAdding: (isAdding: boolean) => void;
    users: User[];
    products: Plan[]; // Assuming Plan includes necessary price info or you fetch prices separately
    onAddInvoice: (newInvoiceData: any) => void; // Adjust 'any' to the expected return type
    setError: (error: string | null) => void;
    onAddUser: (newUser: User) => void; // Add this function to update the users list
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({
    isAdding,
    setIsAdding,
    users,
    products,
    onAddInvoice,
    setError: parentSetError,  // Rename to avoid confusion
    onAddUser
}) => {
    const [selectedUserId, setSelectedUserId] = useState<string>('');
    const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
    const [items, setItems] = useState<InvoiceItemForm[]>([]);
    const [selectedProduct, setSelectedProduct] = useState<string>('');
    const [quantity, setQuantity] = useState<number>(1);
    const [globalBillingCycle, setGlobalBillingCycle] = useState<'monthly' | 'yearly'>('yearly');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [userQuery, setUserQuery] = useState('');
    const [productQuery, setProductQuery] = useState('');

    // New state for user creation
    const [showCreateUserModal, setShowCreateUserModal] = useState(false);
    const [creatingUser, setCreatingUser] = useState(false);
    const [newUser, setNewUser] = useState({
        email: '',
        password: '',
        full_name: '',
        auto_confirm: true
    });
    const [success, setSuccess] = useState<string | null>(null);

    // Local error state
    const [error, setError] = useState<string | null>(null);

    // Use a combined error handler that updates both local and parent error states
    const handleError = (errorMessage: string | null) => {
        setError(errorMessage);
        parentSetError(errorMessage);
    };

    // Filter users based on search query
    const filteredUsers = userQuery === ''
        ? users
        : users.filter((user) =>
            user.email.toLowerCase().includes(userQuery.toLowerCase()) ||
            (user.full_name && user.full_name.toLowerCase().includes(userQuery.toLowerCase()))
        );

    // Filter products based on search query
    const filteredProducts = productQuery === ''
        ? products
        : products.filter((product) =>
            product.name.toLowerCase().includes(productQuery.toLowerCase()) ||
            product.description.toLowerCase().includes(productQuery.toLowerCase())
        );

    useEffect(() => {
        // Reset form when opening/closing
        if (!isAdding) {
            setSelectedUserId('');
            setSelectedCustomerId(null);
            setItems([]);
            setSelectedProduct('');
            setQuantity(1);
            setGlobalBillingCycle('yearly');
            setIsSubmitting(false);
            setUserQuery('');
            setProductQuery('');
        }
    }, [isAdding]);

    useEffect(() => {
        // Find the stripe_customer_id when a user is selected
        const user = users.find(u => u.id === selectedUserId);
        setSelectedCustomerId(user?.stripe_customer_id ?? null);
    }, [selectedUserId, users]);

    // --- Add Price Fetching Logic Here if needed ---
    // useEffect(() => {
    //     if (selectedProduct) {
    //         // Fetch prices associated with the selected product
    //         // Example: fetchPricesForProduct(selectedProduct).then(setPrices);
    //         setSelectedPrice(''); // Reset price selection
    //     } else {
    //         setPrices([]);
    //         setSelectedPrice('');
    //     }
    // }, [selectedProduct]);
    // --- End Price Fetching Logic ---

    const handleAddItem = () => {
        if (!selectedProduct || quantity <= 0) {
            handleError("Please select a product and ensure quantity is positive.");
            return;
        }

        // Find the selected product object from the products prop
        const product = products.find(p => p.id === selectedProduct);

        if (!product) {
            handleError("Selected product not found.");
            return;
        }

        // Get the Stripe Price ID based on the global billing cycle
        const priceId = globalBillingCycle === 'monthly'
            ? product.stripe_monthly_price_id
            : product.stripe_yearly_price_id;

        if (!priceId) {
            handleError(`Selected product (${product.name}) does not have a ${globalBillingCycle} Stripe Price ID configured.`);
            return;
        }

        // Add the item with the correct priceId
        setItems([...items, {
            productId: selectedProduct,
            priceId: priceId,
            quantity,
            billingCycle: globalBillingCycle // Store the billing cycle with the item for display
        }]);

        // Reset inputs
        setSelectedProduct('');
        setQuantity(1);
        setError(null);
    };

    const handleRemoveItem = (index: number) => {
        setItems(items.filter((_, i) => i !== index));
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();

        // Get the selected user
        const selectedUser = users.find(u => u.id === selectedUserId);
        if (!selectedUser) {
            handleError("Please select a valid user.");
            return;
        }

        if (items.length === 0) {
            handleError("Please add at least one item to the invoice.");
            return;
        }

        setIsSubmitting(true);
        setError(null);

        try {
            const { data, error } = await supabase.functions.invoke('create-stripe-invoice', {
                body: {
                    customerId: selectedUser.stripe_customer_id || '', // Pass empty string if null
                    userId: selectedUser.id, // Pass the user ID for creating customer if needed
                    userEmail: selectedUser.email, // Pass email for creating customer if needed
                    subscriptionItems: items.map(item => ({
                        priceId: item.priceId,
                        quantity: item.quantity
                    })),
                },
            });

            if (error) throw error;

            // console.log("Invoice creation response:", data);
            onAddInvoice(data);
            setIsAdding(false);

        } catch (err: any) {
            console.error("Error creating invoice:", err);
            handleError(`Failed to create invoice: ${err.message || err.details || 'Unknown error'}`);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCreateUser = async (e: React.FormEvent & { localUser?: any }) => {
        // Only call preventDefault if it exists (it's a real event)
        if (e && typeof e.preventDefault === 'function') {
            e.preventDefault();
        }

        setCreatingUser(true);
        setError(null);  // Clear local error

        // Use the local state from the modal if available, otherwise use the parent state
        const userToCreate = e.localUser || newUser;

        try {
            // Validation is now handled in the modal component

            // Get the current session for the auth token
            const { data: { session } } = await supabase.auth.getSession();

            if (!session) {
                throw new Error('You must be logged in to perform this action');
            }

            // Call the Edge Function to create the user with auto-confirmation
            const response = await fetch(
                `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-user`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${session.access_token}`
                    },
                    body: JSON.stringify({
                        email: userToCreate.email,
                        password: userToCreate.password,
                        full_name: userToCreate.full_name,
                        auto_confirm: userToCreate.auto_confirm
                    })
                }
            );

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to create user');
            }

            setSuccess(userToCreate.auto_confirm
                ? 'User created successfully (email auto-confirmed)'
                : 'User created successfully');

            // Set the newly created user as the selected user
            if (result.user && result.user.id) {
                // Add the new user to the users array
                const newUserObj = {
                    id: result.user.id,
                    email: userToCreate.email,
                    full_name: userToCreate.full_name,
                    stripe_customer_id: null
                };

                // Update the users list and select the new user
                onAddUser(newUserObj);
                setSelectedUserId(result.user.id);
            }

            // Reset the form
            setNewUser({
                email: '',
                password: '',
                full_name: '',
                auto_confirm: true
            });

            // Hide the form after successful creation
            setShowCreateUserModal(false);

            // Clear success message after 3 seconds
            setTimeout(() => {
                setSuccess(null);
            }, 3000);
        } catch (err: any) {
            const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
            handleError(errorMessage);

            // Clear error message after 5 seconds
            setTimeout(() => {
                handleError(null);
            }, 5000);
        } finally {
            setCreatingUser(false);
        }
    };

    const CreateUserModal = ({
        showModal,
        setShowModal,
        newUser,
        setNewUser,
        handleCreateUser,
        creatingUser,
        error,
        setError
    }) => {
        if (!showModal) return null;

        // Create local state to avoid re-renders from parent component
        const [localUser, setLocalUser] = useState(newUser);

        // Sync local state with parent state when modal opens
        useEffect(() => {
            setLocalUser(newUser);
        }, [newUser]);

        // Handle local input changes
        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setLocalUser({
                ...localUser,
                [name]: type === 'checkbox' ? checked : value
            });
        };

        // Handle form submission with local state
        const handleSubmit = (e) => {
            e.preventDefault();

            // Validate form locally before submitting
            if (!localUser.email || !localUser.password || !localUser.full_name) {
                setError('All fields are required');
                return;
            }

            if (localUser.password.length < 8) {
                setError('Password must be at least 8 characters long');
                return;
            }

            // Update parent state before submitting
            setNewUser(localUser);

            // Call the parent's handleCreateUser function with just the local state
            // Don't try to extend the event object
            handleCreateUser({ localUser });
        };

        return ReactDOM.createPortal(
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100]">
                <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Create New User</h3>
                        <button
                            onClick={() => setShowModal(false)}
                            className="text-gray-400 hover:text-gray-500 focus:outline-none"
                        >
                            <X className="h-5 w-5" />
                        </button>
                    </div>

                    {/* Display error message if present */}
                    {error && (
                        <div className="mb-4 p-3 bg-red-50 text-red-700 text-sm rounded-md">
                            {error}
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="mb-4">
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                value={localUser.email}
                                onChange={handleInputChange}
                                className="mt-1 block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
                                required
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="full_name" className="block text-sm font-medium text-gray-700">Full Name</label>
                            <input
                                type="text"
                                id="full_name"
                                name="full_name"
                                value={localUser.full_name}
                                onChange={handleInputChange}
                                className="mt-1 block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
                                required
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="password" className="block text-sm font-medium text-gray-700">Password</label>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                value={localUser.password}
                                onChange={handleInputChange}
                                className="mt-1 block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
                                required
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="auto_confirm" className="block text-sm font-medium text-gray-700">Auto Confirm Email</label>
                            <input
                                type="checkbox"
                                id="auto_confirm"
                                name="auto_confirm"
                                checked={localUser.auto_confirm}
                                onChange={handleInputChange}
                                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                            />
                        </div>
                        <div className="flex justify-end">
                            <button
                                type="button"
                                onClick={() => setShowModal(false)}
                                className="px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 ml-3"
                                disabled={creatingUser}
                            >
                                {creatingUser ? (
                                    <>
                                        <Loader2 className="animate-spin h-4 w-4 mr-2" />
                                        Creating...
                                    </>
                                ) : (
                                    <>
                                        <Plus className="h-4 w-4 mr-2" />
                                        Create User
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>,
            document.body
        );
    };

    return (
        <>
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex justify-center items-start pt-20 pb-20">
                <div className="relative bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl">
                    <button
                        onClick={() => setIsAdding(false)}
                        className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
                        disabled={isSubmitting}
                    >
                        <X className="h-6 w-6" />
                    </button>
                    <h3 className="text-xl font-semibold text-gray-900 mb-6">Create New Invoice</h3>

                    <form onSubmit={handleSubmit}>
                        {/* Customer Selection - Searchable Combobox */}
                        <div className="mb-4 relative">
                            <div className="flex justify-between items-center mb-1">
                                <label htmlFor="customer" className="block text-sm font-medium text-gray-700">Customer</label>
                                <button
                                    type="button"
                                    onClick={() => {
                                        console.log("Opening create user modal");
                                        setShowCreateUserModal(true);
                                    }}
                                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700"
                                >
                                    <UserPlus className="h-3 w-3 mr-1" />
                                    New User
                                </button>
                            </div>
                            <Combobox
                                as="div"
                                value={selectedUserId}
                                onChange={(value) => setSelectedUserId(value)}
                                disabled={isSubmitting}
                            >
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-4 w-4 text-gray-400" />
                                    </div>
                                    <Combobox.Input
                                        className="w-full pl-10 pr-10 py-2 text-base border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        onChange={(event) => setUserQuery(event.target.value)}
                                        displayValue={(userId: string) => {
                                            const user = users.find(u => u.id === userId);
                                            return user ? `${user.full_name || ''} (${user.email})` : '';
                                        }}
                                        placeholder="Search users..."
                                    />
                                    <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
                                        <X
                                            className={`h-5 w-5 text-gray-400 ${selectedUserId ? 'visible' : 'invisible'}`}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setSelectedUserId('');
                                                setUserQuery('');
                                            }}
                                        />
                                    </Combobox.Button>
                                </div>
                                <div className="relative">
                                    <Combobox.Options className="absolute z-50 mt-1 max-h-40 w-full overflow-y-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                        {filteredUsers.length === 0 && userQuery !== '' ? (
                                            <div className="relative cursor-default select-none py-2 px-4 text-gray-700">
                                                No users found.
                                            </div>
                                        ) : (
                                            filteredUsers.map((user) => (
                                                <Combobox.Option
                                                    key={user.id}
                                                    value={user.id}
                                                    className={({ active }) =>
                                                        `relative cursor-default select-none py-2 pl-3 pr-9 ${active ? 'bg-indigo-600 text-white' : 'text-gray-900'
                                                        }`
                                                    }
                                                >
                                                    {({ active, selected }) => (
                                                        <>
                                                            <div className="flex items-center">
                                                                <span className={`block truncate ${selected ? 'font-semibold' : 'font-normal'}`}>
                                                                    {user.full_name || 'Unnamed'} ({user.email})
                                                                    {!user.stripe_customer_id ? ' (No Stripe ID)' : ''}
                                                                </span>
                                                            </div>
                                                        </>
                                                    )}
                                                </Combobox.Option>
                                            ))
                                        )}
                                    </Combobox.Options>
                                </div>
                            </Combobox>
                            {!selectedCustomerId && selectedUserId && (
                                <p className="mt-1 text-xs text-gray-500">A Stripe Customer ID will be created for this user.</p>
                            )}
                        </div>

                        {/* Global Billing Cycle Toggle */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">Billing Cycle</label>
                            <div className="flex items-center space-x-4">
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="radio"
                                        id="monthly"
                                        name="billingCycle"
                                        value="monthly"
                                        checked={globalBillingCycle === 'monthly'}
                                        onChange={() => setGlobalBillingCycle('monthly')}
                                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                        disabled={isSubmitting}
                                    />
                                    <label htmlFor="monthly" className="text-sm text-gray-700">Monthly</label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="radio"
                                        id="yearly"
                                        name="billingCycle"
                                        value="yearly"
                                        checked={globalBillingCycle === 'yearly'}
                                        onChange={() => setGlobalBillingCycle('yearly')}
                                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                        disabled={isSubmitting}
                                    />
                                    <label htmlFor="yearly" className="text-sm text-gray-700">Yearly</label>
                                </div>
                            </div>
                        </div>

                        {/* Invoice Items Section */}
                        <div className="border-t border-gray-200 pt-4 mt-6">
                            <h4 className="text-lg font-medium text-gray-800 mb-3">Invoice Items</h4>
                            {/* Item Input Row */}
                            <div className="flex items-end space-x-3 mb-4">
                                {/* Product Selection - Searchable Combobox */}
                                <div className="flex-grow relative">
                                    <label htmlFor="product" className="block text-sm font-medium text-gray-700 mb-1">Product</label>
                                    <Combobox
                                        as="div"
                                        value={selectedProduct}
                                        onChange={(value) => setSelectedProduct(value)}
                                        disabled={isSubmitting}
                                    >
                                        <div className="relative">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <Search className="h-4 w-4 text-gray-400" />
                                            </div>
                                            <Combobox.Input
                                                className="w-full pl-10 pr-10 py-2 text-base border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                onChange={(event) => setProductQuery(event.target.value)}
                                                displayValue={(productId: string) => {
                                                    const product = products.find(p => p.id === productId);
                                                    return product ? product.name : '';
                                                }}
                                                placeholder="Search products..."
                                            />
                                            <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
                                                <X
                                                    className={`h-5 w-5 text-gray-400 ${selectedProduct ? 'visible' : 'invisible'}`}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        setSelectedProduct('');
                                                        setProductQuery('');
                                                    }}
                                                />
                                            </Combobox.Button>
                                        </div>
                                        <div className="relative">
                                            <Combobox.Options className="absolute z-50 mt-1 max-h-40 w-full overflow-y-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                                {filteredProducts.length === 0 && productQuery !== '' ? (
                                                    <div className="relative cursor-default select-none py-2 px-4 text-gray-700">
                                                        No products found.
                                                    </div>
                                                ) : (
                                                    filteredProducts.map((product) => (
                                                        <Combobox.Option
                                                            key={product.id}
                                                            value={product.id}
                                                            className={({ active }) =>
                                                                `relative cursor-default select-none py-2 pl-3 pr-9 ${active ? 'bg-indigo-600 text-white' : 'text-gray-900'
                                                                }`
                                                            }
                                                        >
                                                            {({ active, selected }) => (
                                                                <>
                                                                    <div className="flex flex-col">
                                                                        <span className={`block truncate ${selected ? 'font-semibold' : 'font-normal'}`}>
                                                                            {product.name}
                                                                        </span>
                                                                        <span className={`block truncate text-xs ${active ? 'text-indigo-200' : 'text-gray-500'
                                                                            }`}>
                                                                            {product.description?.substring(0, 50)}
                                                                            {product.description?.length > 50 ? '...' : ''}
                                                                        </span>
                                                                    </div>
                                                                </>
                                                            )}
                                                        </Combobox.Option>
                                                    ))
                                                )}
                                            </Combobox.Options>
                                        </div>
                                    </Combobox>
                                </div>

                                {/* Quantity Input */}
                                <div className="w-24">
                                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                                    <input
                                        type="number"
                                        id="quantity"
                                        min="1"
                                        value={quantity}
                                        onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                                        className="mt-1 block w-full pl-3 pr-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
                                        disabled={isSubmitting}
                                    />
                                </div>

                                {/* Add Button */}
                                <button
                                    type="button"
                                    onClick={handleAddItem}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                    disabled={!selectedProduct || quantity <= 0 || isSubmitting}
                                >
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add
                                </button>
                            </div>

                            {/* Added Items List */}
                            <div className="space-y-2 max-h-40 overflow-y-auto">
                                {items.map((item, index) => {
                                    const product = products.find(p => p.id === item.productId);
                                    return (
                                        <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                                            <span className="text-sm text-gray-700">
                                                {product?.name ?? 'Unknown Product'} (Qty: {item.quantity})
                                            </span>
                                            <button
                                                type="button"
                                                onClick={() => handleRemoveItem(index)}
                                                className="text-red-500 hover:text-red-700"
                                                disabled={isSubmitting}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </button>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Form Actions */}
                        <div className="mt-6 pt-4 border-t border-gray-200 flex justify-end space-x-3">
                            <button
                                type="button"
                                onClick={() => setIsAdding(false)}
                                className="px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                                disabled={items.length === 0 || !selectedUserId || isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <Loader2 className="animate-spin h-4 w-4 mr-2" />
                                        Creating...
                                    </>
                                ) : (
                                    <>
                                        <Save className="h-4 w-4 mr-2" />
                                        Create Invoice
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {/* User creation modal */}
            <CreateUserModal
                showModal={showCreateUserModal}
                setShowModal={setShowCreateUserModal}
                newUser={newUser}
                setNewUser={setNewUser}
                handleCreateUser={handleCreateUser}
                creatingUser={creatingUser}
                error={error}
                setError={setError}
            />
        </>
    );
};

export default InvoiceForm;
