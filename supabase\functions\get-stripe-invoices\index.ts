import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@11.16.0?target=deno';
import { corsHeaders } from '../_shared/cors.ts';

// Initialize Stripe client - ensure STRIPE_SECRET_KEY is set in Supabase secrets
const stripe = Stripe(Deno.env.get('STRIPE_SECRET_KEY')!, {
  httpClient: Stripe.createFetchHttpClient(),
  apiVersion: '2023-10-16', // Use your desired API version
});

// Simplified interface for the data returned directly from Stripe + Customer info
// Adjust based on the actual fields you need in the frontend
interface StripeInvoiceWithCustomer {
  id: string; // Stripe Invoice ID
  stripe_invoice_id: string; // Keep for consistency if frontend expects it
  stripe_customer_id: string | null;
  customer_email: string | null;
  customer_name: string | null;
  status: string | null;
  amount_due: number | null;
  amount_paid: number | null;
  amount_remaining: number | null;
  currency: string | null;
  due_date: string | null; // ISO string
  invoice_pdf: string | null;
  hosted_invoice_url: string | null;
  created_at: string; // ISO string from Stripe 'created' timestamp
  // Add other fields from Stripe Invoice object as needed
}


serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // --- Get Pagination Params from Request Body ---
    // Stripe uses cursor-based pagination ('starting_after' or 'ending_before')
    const { limit = 10, starting_after = undefined, count_only = false } = await req.json() || {};
    const fetchLimit = Math.max(1, Math.min(100, parseInt(limit, 10))); // Ensure limit is reasonable (1-100)
    // --- End Pagination Params ---

    if (count_only) {
      try {
        // Request just enough invoices to determine if we're over 99
        const invoices = await stripe.invoices.list({
          limit: 100, // Just enough to check if we have 99+ invoices
        });

        // Get the count from the data array
        const count = invoices.data.length;

        // Check if we have more than 99 or if there are more pages
        const displayCount = (count > 99 || invoices.has_more) ? "99+" : count.toString();

        return new Response(
          JSON.stringify({
            count: count,
            displayCount: displayCount
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200,
          }
        );
      } catch (error) {
        console.error('Error counting invoices:', error);
        return new Response(
          JSON.stringify({
            error: 'Failed to count invoices',
            count: 0,
            displayCount: "0"
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        );
      }
    }

    // --- Fetch Invoices Directly from Stripe ---
    console.log(`Fetching invoices from Stripe with limit: ${fetchLimit}, starting_after: ${starting_after}`);
    const stripeInvoicesResponse = await stripe.invoices.list({
      limit: fetchLimit,
      starting_after: starting_after,
      // You can add other filters here, e.g., status: 'open'
      expand: ['data.customer'], // Expand customer data directly
    });
    console.log(`Fetched ${stripeInvoicesResponse.data.length} invoices from Stripe. Has more: ${stripeInvoicesResponse.has_more}`);
    // --- End Fetch Invoices ---


    // --- Map Stripe Data ---
    const processedInvoices: StripeInvoiceWithCustomer[] = stripeInvoicesResponse.data.map(invoice => {
      // Type guard to check if customer is expanded and not deleted
      const customer = invoice.customer && typeof invoice.customer === 'object' && 'id' in invoice.customer
        ? invoice.customer
        : null;

      return {
        id: invoice.id, // Use Stripe ID
        stripe_invoice_id: invoice.id,
        stripe_customer_id: typeof invoice.customer === 'string' ? invoice.customer : customer?.id ?? null,
        customer_email: customer?.email ?? (typeof invoice.customer_email === 'string' ? invoice.customer_email : null), // Fallback to invoice.customer_email
        customer_name: customer?.name ?? (typeof invoice.customer_name === 'string' ? invoice.customer_name : null), // Fallback to invoice.customer_name
        status: invoice.status,
        amount_due: invoice.amount_due,
        amount_paid: invoice.amount_paid,
        amount_remaining: invoice.amount_remaining,
        currency: invoice.currency,
        due_date: invoice.due_date ? new Date(invoice.due_date * 1000).toISOString() : null,
        // Remove backticks from these assignments
        invoice_pdf: invoice.invoice_pdf,
        hosted_invoice_url: invoice.hosted_invoice_url,
        created_at: new Date(invoice.created * 1000).toISOString(),
        // Map other necessary fields
      };
    });
    // --- End Map Stripe Data ---

    // --- Determine Last Invoice ID for Next Page ---
    const lastInvoiceId = processedInvoices.length > 0 ? processedInvoices[processedInvoices.length - 1].id : null;
    // --- End Determine Last Invoice ID ---

    // --- Remove Supabase DB Query Logic ---
    // const supabaseClient = createClient(...);
    // const { data: dbInvoices, error: dbError, count } = await supabaseClient...
    // if (dbError) { ... }
    // const totalItems = count || 0;
    // const totalPages = Math.ceil(totalItems / sizeNum);
    // --- End Remove Supabase DB Query Logic ---


    // Return the fetched Stripe data and pagination info
    return new Response(
      JSON.stringify({
        // Use a consistent key, e.g., 'invoices' or 'data'
        invoices: processedInvoices,
        has_more: stripeInvoicesResponse.has_more, // Let the client know if there are more pages
        last_invoice_id: lastInvoiceId, // Send the ID for the next 'starting_after'
        // Remove totalItems, totalPages, currentPage as they don't directly map to cursor pagination
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('Function Error:', error);
    // Check if the error is a Stripe error for more specific messages
    let errorMessage = error.message;
    if (error.type) { // Basic check for Stripe error object structure
      errorMessage = `Stripe Error (${error.type}): ${error.message}`;
    }
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: error.statusCode || 500, // Use Stripe's status code if available
    });
  }
});

// --- Remove SQL Schema Comments ---
/*
-- Example SQL for the 'invoices' table (adjust as needed)
CREATE TABLE invoices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    stripe_invoice_id TEXT UNIQUE NOT NULL,
    stripe_customer_id TEXT,
    stripe_subscription_id TEXT, -- Optional: Link to subscription
    status TEXT NOT NULL, -- e.g., 'draft', 'open', 'paid', 'uncollectible', 'void'
    amount_due BIGINT, -- Store in cents
    amount_paid BIGINT, -- Store in cents
    amount_remaining BIGINT, -- Store in cents
    currency TEXT NOT NULL,
    due_date TIMESTAMPTZ,
    invoice_pdf TEXT, -- URL to the PDF
    hosted_invoice_url TEXT, -- URL to the hosted invoice page
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    -- Add any other relevant fields from the Stripe Invoice object you want to sync
    raw_stripe_data JSONB -- Optional: Store the full Stripe object
);

-- Enable RLS
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to view their own invoices (example)
CREATE POLICY "Allow users to view their own invoices"
ON invoices
FOR SELECT
USING (auth.uid() = user_id);

-- Policy: Allow admin users to view all invoices (example - requires custom claims or role check)
CREATE POLICY "Allow admins to view all invoices"
ON invoices
FOR SELECT
USING (public.is_admin(auth.uid())); -- Assumes an is_admin function

-- Trigger to update 'updated_at' timestamp
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON invoices
  FOR EACH ROW EXECUTE PROCEDURE moddatetime (updated_at);

-- Index common query fields
CREATE INDEX idx_invoices_user_id ON invoices(user_id);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_created_at ON invoices(created_at);
CREATE INDEX idx_invoices_stripe_invoice_id ON invoices(stripe_invoice_id);

*/
