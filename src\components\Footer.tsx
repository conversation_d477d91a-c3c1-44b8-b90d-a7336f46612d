import React, { useEffect, useState } from 'react';
import { Activity, Mail, Phone, MapPin, Linkedin, Twitter, Facebook, Send, ArrowRight, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import irsLogo from '../assets/images/international_responder_systems_logo-rbg.png';
import { useSiteSettings } from '../hooks/useSiteSettings';

export default function Footer() {
  const user = useAuthStore((state) => state.user);
  const [email, setEmail] = useState('');
  const [subscribeStatus, setSubscribeStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [subscriberCount, setSubscriberCount] = useState(0);
  const { settings, loading } = useSiteSettings();

  // Fetch subscriber count on component mount
  useEffect(() => {
    const fetchSubscriberCount = async () => {
      try {
        // First try with exact column names
        const { data, count, error } = await supabase
          .from('subscribers')
          .select('*', { count: 'exact' })
          .eq('status', 'active');

        if (error || count === null) {

          setSubscriberCount(altCount || 0);
        } else {
          setSubscriberCount(count);
        }
      } catch (err) {
        console.error('Error fetching subscriber count:', err);
        // Fallback to showing the count from SQL query
        setSubscriberCount(2);
      }
    };

    fetchSubscriberCount();
  }, []);

  // Format subscriber count for display
  const formattedSubscriberCount = subscriberCount >= 1000
    ? `${(subscriberCount / 1000).toFixed(1)}k+`
    : `${subscriberCount}+`;

  const solutions = [
    { name: "Products", path: "/products" },
    { name: 'GrantReady™', path: '/solutions/grantready' },
    { name: 'SOAR', path: '/solutions/soar' },
    { name: 'ELENOR', path: '/solutions/elenor' }
  ];

  const resources = [
    { name: 'Blog', path: '/blog' },
    { name: 'Case Studies', path: '/case-studies' },
    { name: 'Webinars', path: '/webinars' },
    { name: 'Whitepapers', path: '/whitepapers' },
    { name: 'Guides', path: '/guides' },
    { name: 'Events', path: '/events' }
  ];

  const company = [
    { name: 'About Us', path: '/about' },
    { name: 'Leadership Team', path: '/leadership' },
    { name: 'News', path: '/news' },
    { name: 'Careers', path: '/careers' }
  ];

  const support = [
    { name: 'Contact', path: '/contact' },
    { name: 'FAQ', path: '/faq' }
  ];

  const legal = [
    { name: 'Terms & Conditions', path: '/terms' },
    { name: 'Privacy Policy', path: '/privacy' },
    { name: 'Accessibility', path: '/accessibility' }
  ];

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubscribeStatus('loading');
    setStatusMessage('');

    const emailToCheck = user ? user.email : email;
    const name = user ? user.user_metadata?.full_name || 'Guest' : 'Guest';

    // Check if email is empty
    if (!emailToCheck || emailToCheck.trim() === '') {
      setSubscribeStatus('error');
      setStatusMessage('Please enter your email address.');
      return;
    }

    try {
      // if(emailToCheck.trim === '')

      // First check if email is already subscribed and active
      const { data: existingSubscriber } = await supabase
        .from('subscribers')
        .select('status')
        .eq('email', emailToCheck)
        .eq('status', 'active')
        .maybeSingle();

      if (existingSubscriber) {
        throw new Error('This email is already subscribed');
      }

      // Add to database
      const { error: dbError } = await supabase
        .from('subscribers')
        .insert({
          email: emailToCheck,
          full_name: name,
          status: 'active',
          subscribed_at: new Date().toISOString()
        });

      if (dbError) {
        throw new Error(dbError.message || 'Failed to save subscription');
      }

      // Call the Supabase function to send email
      const { error: emailError } = await supabase.functions.invoke('send-subscription-email', {
        body: {
          to: emailToCheck,
          subject: "Welcome to Our Newsletter!",
          text: `Hello ${name || "Subscriber"},
      
      Thank you for subscribing to International Responder Systems' newsletter!
      Stay tuned for updates on healthcare emergency response and grant management.
      
      Best regards,
      International Responder Systems`
        }
      });


      if (emailError) {
        throw new Error(`Failed to send confirmation email: ${emailError.message}`);
      }

      setSubscribeStatus('success');
      setStatusMessage('Thank you for subscribing! Check your inbox.');
      setEmail('');
      setSubscriberCount(prev => prev + 1); // Update subscriber count
    } catch (err: any) {
      console.error('Subscription error:', err);
      setSubscribeStatus('error');
      setStatusMessage(err.message || 'Subscription failed. Please try again.');
    }
  };

  if (loading) return null; // Or loading spinner

  return (
    <footer className="bg-gray-900 text-gray-300">
      {/* Newsletter Section */}
      <div className="border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Newsletter Form */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg blur-xl"></div>
              <div className="relative bg-white/5 backdrop-blur-sm p-8 rounded-lg border border-white/10">
                <h3 className="text-2xl font-bold text-white mb-2">Stay Updated</h3>
                <p className="text-gray-400 mb-6">
                  Get the latest updates on healthcare emergency response and grant management.
                </p>
                <form onSubmit={handleSubscribe} className="space-y-4">
                  <div className="relative">
                    <input
                      type="email"
                      placeholder="Enter your email"
                      value={user ? user.email : email}
                      onChange={(e) => {
                        if (!user) {
                          setEmail(e.target.value);
                        }
                      }}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
                      disabled={!!user}
                    />
                    <button
                      type="submit"
                      disabled={subscribeStatus === 'loading'}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center"
                    >
                      {subscribeStatus === 'loading' ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Subscribe
                        </>
                      )}
                    </button>
                  </div>
                  {subscribeStatus !== 'idle' && (
                    <p className={`text-sm ${subscribeStatus === 'success' ? 'text-green-400' : 'text-red-400'
                      }`}>
                      {statusMessage}
                    </p>
                  )}
                </form>
              </div>
            </div>

            {/* Newsletter Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10">
                <div className="text-3xl font-bold text-white mb-1">{formattedSubscriberCount}</div>
                <div className="text-gray-400">Subscribers</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10">
                <div className="text-3xl font-bold text-white mb-1">98%</div>
                <div className="text-gray-400">Satisfaction</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10">
                <div className="text-3xl font-bold text-white mb-1">24/7</div>
                <div className="text-gray-400">Support</div>
              </div>
              <div className="bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10">
                <div className="text-3xl font-bold text-white mb-1">50+</div>
                <div className="text-gray-400">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-2 md:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="col-span-2">
            <div className="flex items-center mb-6">
              <img src={irsLogo} alt="IRS Logo" className="h-12 w-12" />
            </div>
            <p className="text-gray-400 mb-6">
              {settings?.general?.siteDescription || 'Healthcare Emergency Response Solutions'}
            </p>
            <div className="space-y-3">
              <a
                href={`mailto:${settings?.general?.contactEmail}`}
                className="flex items-center text-gray-400 hover:text-white"
              >
                <Mail className="h-5 w-5 mr-2" />
                {settings?.general?.contactEmail}
              </a>
              <div className="flex items-center text-gray-400">
                <MapPin className="h-5 w-5 mr-2" />
                {settings?.general?.address || '157 E Main Street, Elkton, MD 21921-5977'}
              </div>
              <div className='flex items-center text-gray-400'>
                <Phone className="h-5 w-5 mr-2" />
                {settings?.general?.phone}
              </div>
            </div>
          </div>

          {/* Solutions */}
          <div>
            <h3 className="text-white font-semibold mb-4">Solutions</h3>
            <ul className="space-y-3">
              {solutions.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-400 hover:text-white">
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-white font-semibold mb-4">Resources</h3>
            <ul className="space-y-3">
              {resources.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-400 hover:text-white">
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-white font-semibold mb-4">Company</h3>
            <ul className="space-y-3">
              {company.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-400 hover:text-white">
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support & Legal */}
          <div>
            <h3 className="text-white font-semibold mb-4">Support</h3>
            <ul className="space-y-3">
              {support.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-400 hover:text-white">
                    {item.name}
                  </Link>
                </li>
              ))}
              {legal.map((item) => (
                <li key={item.path}>
                  <Link to={item.path} className="text-gray-400 hover:text-white">
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Social Links & Copyright */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex space-x-6">
              <a
                href={settings?.general?.socialLinks?.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a
                href={settings?.general?.socialLinks?.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white h-4 w-4 mt-auto"
              >
                {/* <X className="h-5 w-5" /> */}
                <svg viewBox="0 0 22 22" aria-hidden="true" className="r-4qtqp9 r-yyyyoo r-dnmrzs r-bnwqim r-lrvibr r-m6rgpd r-k200y r-18jsvk2 r-5sfk15 r-kzbkwu">
                  <g>
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" stroke="#FFFFFF"></path>
                  </g>
                </svg>
              </a>
              <a
                href={settings?.general?.socialLinks?.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white"
              >
                <Facebook className="h-5 w-5" />
              </a>
            </div>
            <p className="mt-4 md:mt-0 text-gray-400">
              © {new Date().getFullYear()} {settings?.general?.siteName || 'International Responder Systems'}. All Rights Reserved
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}