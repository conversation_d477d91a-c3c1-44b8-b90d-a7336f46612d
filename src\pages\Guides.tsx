import React, { useState, useEffect, useCallback } from 'react';
import { FileText, Download, Search } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import LoginModal from '../components/LoginModal';

interface Guide {
  id: string;
  title: string;
  description: string;
  image_url?: string;
  pdf_url?: string;
  category: string;
  type: string;
  is_public: boolean;
  download_count: number;
  created_at: string;
}

export default function Guides() {
  const [guides, setGuides] = useState<Guide[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const user = useAuthStore((state) => state.user);

  const categories = [
    "Emergency Response",
    "Grant Management",
    "Healthcare Data",
    "Compliance",
    "Best Practices",
    "Implementation"
  ];

  const fetchGuides = useCallback(async () => {
    try {
      let query = supabase
        .from('guides')
        .select('*')
        .eq('type', 'guide') // Only fetch items with type 'guide'
        .order('created_at', { ascending: false });

      if (!user) {
        query = query.eq('is_public', true);
      }

      if (selectedCategory) {
        query = query.eq('category', selectedCategory);
      }

      if (searchQuery) {
        query = query.ilike('title', `%${searchQuery}%`);
      }

      const { data, error } = await query;

      if (error) throw error;
      setGuides(data);
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [user, selectedCategory, searchQuery]);

  useEffect(() => {
    fetchGuides();
  }, [fetchGuides]);

  const handleDownload = useCallback(async (guideId: string, pdfUrl: string) => {
    // Check if user is authenticated
    if (!user) {
      // Show login modal
      setSelectedPdf({ id: guideId, url: pdfUrl });
      setShowLoginModal(true);
      return;
    }

    try {
      // First get current download count
      const { data: guideData, error: fetchError } = await supabase
        .from('guides')
        .select('download_count')
        .eq('id', guideId)
        .single();

      if (fetchError) throw fetchError;

      // Increment download count
      const { error: updateError } = await supabase
        .from('guides')
        .update({ download_count: (guideData.download_count || 0) + 1 })
        .eq('id', guideId);

      if (updateError) throw updateError;

      // Handle download
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = pdfUrl.split('/').pop() || 'guide.pdf';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Refresh guides to show updated count
      fetchGuides();
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || 'Failed to download guide');
    }
  }, [user, fetchGuides]);

  // Update the Featured Guides section render:
  // Update the search input in the Hero section:
  const [expandedDescriptions, setExpandedDescriptions] = useState<Record<string, boolean>>({});
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState<{ id: string, url: string } | null>(null);

  const toggleDescription = (guideId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [guideId]: !prev[guideId]
    }));
  };

  // Handle closing the login modal
  const handleCloseLoginModal = () => {
    setShowLoginModal(false);
    setSelectedPdf(null);
  };

  // Handle continuing with download after login
  const handleContinueDownload = useCallback(() => {
    if (selectedPdf && user) {
      handleDownload(selectedPdf.id, selectedPdf.url);
      setSelectedPdf(null);
    }
  }, [selectedPdf, user, handleDownload]);

  // Check if user changed and we have a pending download
  useEffect(() => {
    if (user && selectedPdf) {
      handleContinueDownload();
    }
  }, [user, selectedPdf, handleContinueDownload]);

  return (
    <div className="pt-16">
      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={handleCloseLoginModal}
        title="Download Guide"
        message="Please log in to download this guide. Logging in allows us to save your information for future downloads."
      />

      {/* Error display */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 mx-4 sm:mx-6 lg:mx-8">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  onClick={() => setError(null)}
                  className="inline-flex rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-left">
            <h1 className="text-4xl font-bold mb-6">Resource Guides</h1>
            <p className="text-xl mb-10 text-blue-100">
              Access comprehensive guides and documentation to enhance your healthcare emergency response capabilities
            </p>
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search guides..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => setSelectedCategory('')}
              className={`px-6 py-2 rounded-full shadow-sm hover:shadow-md transition-shadow font-medium ${
                selectedCategory === ''
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:text-blue-600'
              }`}
            >
              All Categories
            </button>
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-2 rounded-full shadow-sm hover:shadow-md transition-shadow font-medium ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:text-blue-600'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Guides */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold mb-8">Featured Guides</h2>

          {loading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : guides.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 px-4 bg-white rounded-lg shadow-sm">
              <FileText className="h-16 w-16 text-blue-200 mb-4" />
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No Guides Available</h3>
              <p className="text-gray-500 text-center max-w-md mb-6">
                {searchQuery && selectedCategory
                  ? `No guides found matching "${searchQuery}" in the "${selectedCategory}" category.`
                  : searchQuery
                    ? `No guides found matching "${searchQuery}".`
                    : selectedCategory
                      ? `No guides available in the "${selectedCategory}" category yet.`
                      : "No guides available yet. Please check back later."}
              </p>
              {(searchQuery || selectedCategory) && (
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('');
                  }}
                  className="px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                >
                  Clear Filters
                </button>
              )}
            </div>
          ) : (
            <div className="grid md:grid-cols-3 gap-8">
            {guides.map((guide) => (
              <div key={guide.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                {guide.image_url && (
                  <img
                    src={guide.image_url}
                    alt={guide.title}
                    className="w-full h-48 object-cover"
                  />
                )}
                <div className="p-6">
                  <span className="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                    {guide.category}
                  </span>
                  <h3 className="text-xl font-bold mt-4 mb-2">{guide.title}</h3>
                  <div className="mb-4">
                    <p className="text-gray-600">
                      {expandedDescriptions[guide.id]
                        ? guide.description
                        : `${guide.description?.substring(0, 70)}${guide.description?.length > 70 ? '...' : ''}`
                      }
                    </p>
                    {guide.description?.length > 70 && (
                      <button
                        onClick={() => toggleDescription(guide.id)}
                        className="text-blue-600 text-sm mt-1 hover:underline"
                      >
                        {expandedDescriptions[guide.id] ? 'See Less' : 'See More'}
                      </button>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-500">
                      <Download className="h-4 w-4 mr-1" />
                      <span>{guide.download_count} downloads</span>
                    </div>
                    <button
                      onClick={() => guide.pdf_url && handleDownload(guide.id, guide.pdf_url)}
                      className="flex items-center text-blue-600 font-medium hover:text-blue-700"
                      disabled={!guide.pdf_url}
                    >
                      Download PDF
                      <FileText className="h-4 w-4 ml-2" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
}