import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { Save, Globe, Share2, Mail, Phone, MapPin, AlertCircle, CheckCircle, Search, Link as LinkIcon, Settings as SettingsIcon } from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import { useAuth } from '../../lib/AuthContext';

interface SiteSettings {
  general: {
    siteName: string;
    siteDescription: string;
    contactEmail: string;
    phone: string;
    address: string;
    socialLinks: {
      linkedin: string;
      twitter: string;
      facebook: string;
    };
  };
  seo: {
    defaultTitle: string;
    defaultDescription: string;
    defaultKeywords: string;
    googleAnalyticsId: string;
    googleTagManagerId: string;
    googleSiteVerification: string;
    bingVerification: string;
    robotsTxt: string;
    sitemapEnabled: boolean;
  };
  appearance: {
    logo: string;
    favicon: string;
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
  };
  features: {
    blogEnabled: boolean;
    commentsEnabled: boolean;
    userRegistrationEnabled: boolean;
    maintenanceMode: boolean;
  };
}

export default function SettingsManagement() {
  const [settings, setSettings] = useState<SiteSettings>({
    general: {
      siteName: '',
      siteDescription: '',
      contactEmail: '',
      phone: '',
      address: '',
      socialLinks: {
        linkedin: '',
        twitter: '',
        facebook: ''
      }
    },
    seo: {
      defaultTitle: '',
      defaultDescription: '',
      defaultKeywords: '',
      googleAnalyticsId: '',
      googleTagManagerId: '',
      googleSiteVerification: '',
      bingVerification: '',
      robotsTxt: '',
      sitemapEnabled: false
    },
    appearance: {
      logo: '',
      favicon: '',
      primaryColor: '#2563eb',
      secondaryColor: '#1e40af',
      fontFamily: 'Inter'
    },
    features: {
      blogEnabled: false,
      commentsEnabled: false,
      userRegistrationEnabled: false,
      maintenanceMode: false
    }
  });
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('site_settings')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;
      if (data && data.length > 0) {
        setSettings({
          ...settings,
          ...data[0].settings
        });
      }
    } catch (err: any) {
      console.error('Error fetching settings:', err);
      setError(err.message);
    }
  };

  const handleMaintenanceModeChange = (checked: boolean) => {
    setSettings({
      ...settings,
      features: {
        ...settings.features,
        maintenanceMode: checked
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Show confirmation dialog
    const confirmed = window.confirm('Are you sure you want to save these settings?');
    if (!confirmed) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // First try to get the latest settings record
      const { data: existing } = await supabase
        .from('site_settings')
        .select('id')
        .order('created_at', { ascending: false })
        .limit(1);

      let { error } = await supabase
        .from('site_settings')
        .upsert({
          id: existing?.[0]?.id, // Use existing ID if found
          settings,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
      setSuccess('Settings saved successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'general', label: 'General', icon: SettingsIcon },
    { id: 'seo', label: 'SEO', icon: Search },
    { id: 'features', label: 'Features', icon: Share2 }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Site Name</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.general.siteName}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, siteName: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <SettingsIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Site Description</label>
              <div className="mt-1">
                <textarea
                  value={settings.general.siteDescription}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, siteDescription: e.target.value }
                  })}
                  rows={3}
                  className="block w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Contact Email</label>
              <div className="mt-1 relative">
                <input
                  type="email"
                  value={settings.general.contactEmail}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, contactEmail: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Phone</label>
              <div className="mt-1 relative">
                <input
                  type="tel"
                  value={settings.general.phone}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, phone: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Address</label>
              <div className="mt-1 relative">
                <textarea
                  value={settings.general.address}
                  onChange={(e) => setSettings({
                    ...settings,
                    general: { ...settings.general, address: e.target.value }
                  })}
                  rows={2}
                  className="block pl-8 w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <MapPin className="absolute left-3 top-4 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Social Links</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700">LinkedIn</label>
                <div className="mt-1 relative">
                  <input
                    type="url"
                    value={settings.general.socialLinks.linkedin}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: {
                        ...settings.general,
                        socialLinks: { ...settings.general.socialLinks, linkedin: e.target.value }
                      }
                    })}
                    className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                  />
                  <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </div>
              <div>
                <label className="flex items-baseline justify-start text-xs italic font-medium text-gray-700 gap-x-2 w-fit">
                  <span className='w-4 h-4'>
                    <svg viewBox="0 0 22 22" aria-hidden="true" className="r-4qtqp9 r-yyyyoo r-dnmrzs r-bnwqim r-lrvibr r-m6rgpd r-k200y r-18jsvk2 r-5sfk15 r-kzbkwu">
                      <g>
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" stroke="#FFFFFF"></path>
                      </g>
                    </svg>
                  </span>
                  (Twitter)
                </label>
                <div className="mt-1 relative">
                  <input
                    type="url"
                    value={settings.general.socialLinks.twitter}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: {
                        ...settings.general,
                        socialLinks: { ...settings.general.socialLinks, twitter: e.target.value }
                      }
                    })}
                    className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                  />
                  <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Facebook</label>
                <div className="mt-1 relative">
                  <input
                    type="url"
                    value={settings.general.socialLinks.facebook}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: {
                        ...settings.general,
                        socialLinks: { ...settings.general.socialLinks, facebook: e.target.value }
                      }
                    })}
                    className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                  />
                  <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </div>
            </div>
          </div>
        );

      case 'seo':
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Default Meta Title</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.defaultTitle}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, defaultTitle: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-1 text-sm text-gray-500">Recommended length: 50-60 characters</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Default Meta Description</label>
              <div className="mt-1">
                <textarea
                  value={settings.seo.defaultDescription}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, defaultDescription: e.target.value }
                  })}
                  rows={3}
                  className="block w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
              </div>
              <p className="mt-1 text-sm text-gray-500">Recommended length: 150-160 characters</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Default Keywords</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.defaultKeywords}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, defaultKeywords: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <p className="mt-1 text-sm text-gray-500">Comma-separated keywords</p>
            </div>

            {/* <div>
              <label className="block text-sm font-medium text-gray-700">Google Analytics ID</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.googleAnalyticsId}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, googleAnalyticsId: e.target.value }
                  })}
                  placeholder="G-XXXXXXXXXX"
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div> */}

            <div>
              <label className="block text-sm font-medium text-gray-700">Google Tag Manager ID</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.googleTagManagerId}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, googleTagManagerId: e.target.value }
                  })}
                  placeholder="GTM-XXXXXXXXXX"
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Google Site Verification</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.googleSiteVerification}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, googleSiteVerification: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Bing Verification</label>
              <div className="mt-1 relative">
                <input
                  type="text"
                  value={settings.seo.bingVerification}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, bingVerification: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Favicon URL</label>
              <div className="mt-1 relative">
                <input
                  type="url"
                  value={settings.appearance.favicon}
                  onChange={(e) => setSettings({
                    ...settings,
                    appearance: { ...settings.appearance, favicon: e.target.value }
                  })}
                  className="block w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400"
                />
                <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </div>

            {/* <div>
              <label className="block text-sm font-medium text-gray-700">robots.txt Content</label>
              <div className="mt-1">
                <textarea
                  value={settings.seo.robotsTxt}
                  onChange={(e) => setSettings({
                    ...settings,
                    seo: { ...settings.seo, robotsTxt: e.target.value }
                  })}
                  rows={4}
                  className="block w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 ease-in-out hover:border-gray-400 font-mono"
                />
              </div>
            </div> */}

          </div>
        );
      case 'features':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Blog</h3>
                <p className="text-sm text-gray-500">Enable or disable the blog functionality</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.blogEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, blogEnabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Comments</h3>
                <p className="text-sm text-gray-500">Allow users to comment on blog posts</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.commentsEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, commentsEnabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">User Registration</h3>
                <p className="text-sm text-gray-500">Allow new users to register</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.userRegistrationEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    features: { ...settings.features, userRegistrationEnabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Maintenance Mode</h3>
                <p className="text-sm text-gray-500">Enable to put the site under maintenance</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.features.maintenanceMode}
                  onChange={(e) => handleMaintenanceModeChange(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-12 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-6 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6 max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 p-6 rounded-lg shadow-lg">
        <div className="flex items-center space-x-4">
          <SettingsIcon className="h-8 w-8 text-white" />
          <div>
            <h2 className="text-2xl font-bold text-white">Site Settings</h2>
            <p className="text-blue-100">Manage your website's configuration and appearance</p>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-lg flex items-center shadow-sm">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-500 text-green-700 p-4 rounded-lg flex items-center shadow-sm">
          <CheckCircle className="h-5 w-5 mr-2" />
          {success}
        </div>
      )}

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Tabs */}
        <div className="border-b border-gray-200 bg-gray-50">
          <nav className="flex -mb-px px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-4 inline-flex items-center border-b-2 font-medium text-sm transition-colors duration-200 ${activeTab === tab.id
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                <tab.icon className="h-5 w-5 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-6 space-y-8">
            {/* Tab Content */}
            <div className="space-y-6">
              {renderTabContent()}
            </div>

            {/* Save Button */}
            <div className="pt-6 border-t border-gray-200 flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="-ml-1 mr-3 h-5 w-5" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );


}