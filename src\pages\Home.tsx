import React, { useEffect, useRef, useState } from 'react';
import { Shield, Award, Zap, ArrowRight, FileText, Brain, Building2, Users, ChevronRight, Globe, Lock, ExternalLink, Activity, Settings, CheckCircle, Phone, Briefcase, Package } from 'lucide-react';
import { Link } from 'react-router-dom';
import PassionLedUsHere from '../components/homePage/PassionLedUsHere';
import OurSolutions from '../components/homePage/ourSolutions';

function Home() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef<HTMLDivElement>(null);
  const cursorRef = useRef<HTMLDivElement>(null);

  const slides = [
    {
      title: "Revolutionizing Healthcare Response Systems",
      description: "Empowering healthcare organizations with intelligent solutions for better patient outcomes",
      color: "from-blue-600 to-blue-800",
      image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80",
      link: "/solutions",
      linkText: "Explore Solutions"
    },
    {
      title: "Predict Tomorrow, Act Today",
      description: "Advanced epidemic forecasting and outbreak prediction powered by cutting-edge AI",
      color: "from-orange-500 to-orange-700",
      image: "https://images.unsplash.com/photo-1584036561566-baf8f5f1b144?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80",
      link: "/solutions/soar",
      linkText: "Discover SOAR"
    },
    {
      title: "Situational Awareness at your Fingertips",
      description: "Advanced emergency response and coordination platform for real-time decision making",
      color: "from-yellow-500 to-yellow-700",
      image: "https://images.unsplash.com/photo-1581093588401-fbb62a02f120?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80",
      link: "/solutions/elenor",
      linkText: "Explore ELENOR"
    },
    {
      title: "IT Consulting & Software Engineering",
      description: "Custom solutions for government, state, local & healthcare industry needs",
      color: "from-purple-600 to-purple-800",
      image: "https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80",
      link: "/solutions",
      linkText: "Learn More"
    }
  ];

  const solutions = [
    {
      title: 'GrantReady™',
      description: 'Streamline your grant management process with our comprehensive solution.',
      icon: FileText,
      link: '/solutions/grantready',
      color: 'from-purple-500 to-purple-600',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
    },
    {
      title: 'SOAR',
      description: 'STLT Outbreak Analytics & Response tailored for healthcare providers.',
      icon: Lock,
      link: '/solutions/soar',
      color: 'from-orange-500 to-orange-600',
      image: 'https://images.unsplash.com/photo-1563986768494-4dee2763ff3f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
    },
    {
      title: 'ELENOR',
      description: 'Advanced emergency response and coordination platform.',
      icon: Zap,
      link: '/solutions/elenor',
      color: 'from-yellow-500 to-yellow-600',
      image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80'
    }
  ];

  const capabilities = [
    { icon: Activity, title: 'Virtual Training', description: 'Comprehensive online training and webinars' },
    { icon: Settings, title: 'Management', description: 'Efficient project and program management' },
    { icon: Brain, title: 'Development', description: 'Custom solution development' },
    { icon: Users, title: 'Leadership Prep', description: 'Leadership development and training' },
    { icon: FileText, title: 'Analytics', description: 'Advanced data analytics and insights' },
    { icon: CheckCircle, title: 'Compliance', description: 'Regulatory compliance management' },
    { icon: Shield, title: 'Assessment', description: 'Risk and vulnerability assessment' },
    { icon: Globe, title: 'Consulting', description: 'Expert consulting services' },
    { icon: Phone, title: 'Services', description: '24/7 support and assistance' }
  ];

  const features = [
    {
      icon: Award,
      title: 'Industry Leadership',
      description: 'Over 50 years of combined experience in healthcare emergency response'
    },
    {
      icon: Shield,
      title: 'Proven Security',
      description: 'State-of-the-art security measures protecting sensitive healthcare data'
    },
    {
      icon: Users,
      title: 'Expert Support',
      description: '24/7 dedicated support team ready to assist with any challenges'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!cursorRef.current || currentSlide !== 3) return;

      const { clientX, clientY } = e;
      const cursor = cursorRef.current;
      const rect = cursor.getBoundingClientRect();
      const x = clientX - rect.width / 2;
      const y = clientY - rect.height / 2;

      cursor.style.transform = `translate(${x}px, ${y}px)`;

      const ripple = document.createElement('div');
      ripple.className = 'absolute w-4 h-4 bg-purple-400/30 rounded-full';
      ripple.style.left = `${clientX}px`;
      ripple.style.top = `${clientY}px`;
      document.body.appendChild(ripple);

      ripple.animate(
        [
          { transform: 'scale(0)', opacity: 1 },
          { transform: 'scale(4)', opacity: 0 }
        ],
        {
          duration: 1000,
          easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }
      ).onfinish = () => ripple.remove();
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [currentSlide]);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const parallaxElements = document.querySelectorAll('[data-scroll]');

      parallaxElements.forEach((element: Element) => {
        const speed = (element as HTMLElement).dataset.scrollSpeed || '1';
        const yPos = scrolled * parseFloat(speed) * 0.1;
        (element as HTMLElement).style.transform = `translateY(${yPos}px)`;
      });
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const cards = document.querySelectorAll('.transform-3d');

    cards.forEach((card) => {
      card.addEventListener('mousemove', (e: any) => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        const rotateX = (y - centerY) / 20;
        const rotateY = (centerX - x) / 20;

        (card as HTMLElement).style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
      });

      card.addEventListener('mouseleave', () => {
        (card as HTMLElement).style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
      });
    });
  }, []);

  return (
    <div className="pt-16">
      <section className="relative h-[78vh] min-h-[500px] max-h-[650px] md:max-h-[650px] overflow-hidden">
        <div
          ref={cursorRef}
          className={`fixed w-8 h-8 bg-purple-500/20 rounded-full blur-lg pointer-events-none transition-transform duration-100 ease-out ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'}`}
        ></div>

        <div
          ref={sliderRef}
          className="relative h-full w-full flex overflow-hidden"
        >
          {slides.map((slide, index) => (
            <div
              key={index}
              className={`absolute inset-0 w-full h-full flex-shrink-0 transition-transform duration-700 ease-out`}
              style={{ transform: `translateX(${(index - currentSlide) * 100}%)` }}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${slide.color}`}>
                <div className="absolute inset-0 opacity-20">
                  <div className="absolute -top-1/2 -left-1/2 w-full h-full rounded-full bg-white/20 blur-3xl animate-[spin_30s_linear_infinite]"></div>
                  <div className="absolute -bottom-1/2 -right-1/2 w-full h-full rounded-full bg-white/20 blur-3xl animate-[spin_25s_linear_infinite_reverse]"></div>
                </div>
              </div>

              <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
                <div className="grid md:grid-cols-2 gap-6 md:gap-12 items-center h-full py-8 md:py-0">
                  <div className="text-white text-center md:text-left">
                    <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6 animate-[fadeIn_0.5s_ease-out]">
                      {slide.title}
                    </h1>
                    <p className="text-lg md:text-xl mb-6 md:mb-8 text-white/90 animate-[fadeIn_0.5s_ease-out_0.2s]">
                      {slide.description}
                    </p>
                    <Link
                      to={slide.link}
                      className="micro-button inline-flex items-center justify-center md:justify-start bg-white/10 backdrop-blur-sm border-2 border-white/20 px-6 py-3 rounded-lg font-semibold text-white transition-all duration-300 animate-[fadeIn_0.5s_ease-out_0.4s] hover:bg-white/20 w-full md:w-auto"
                    >
                      <span>{slide.linkText}</span>
                      <ArrowRight className="ml-2 h-5 w-5 icon" />
                    </Link>
                  </div>
                  <div className="relative hidden md:block animate-[slideIn_0.5s_ease-out_0.3s]">
                    <div className="image-3d-container">
                      <div className="image-3d bg-white/10 backdrop-blur-md p-6 rounded-xl transform hover:scale-[1.02] transition-all duration-300 hover:shadow-2xl">
                        <img
                          src={slide.image}
                          alt={slide.title}
                          className="w-full aspect-[4/3] rounded-lg object-cover"
                        />
                        {index === 0 && (
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent rounded-lg">
                            <div className="absolute bottom-4 left-4 right-4">
                              <div className="grid grid-cols-3 gap-4">
                                <div className="bg-white/20 backdrop-blur-md p-3 rounded-lg animate-[fadeIn_0.5s_ease-out_0.2s] opacity-0 [animation-fill-mode:forwards]">
                                  <div className="text-sm font-medium">Success Rate</div>
                                  <div className="text-2xl font-bold">99%</div>
                                </div>
                                <div className="bg-white/20 backdrop-blur-md p-3 rounded-lg animate-[fadeIn_0.5s_ease-out_0.4s] opacity-0 [animation-fill-mode:forwards]">
                                  <div className="text-sm font-medium">Clients</div>
                                  <div className="text-2xl font-bold">1000+</div>
                                </div>
                                <div className="bg-white/20 backdrop-blur-md p-3 rounded-lg animate-[fadeIn_0.5s_ease-out_0.6s] opacity-0 [animation-fill-mode:forwards]">
                                  <div className="text-sm font-medium">Experience</div>
                                  <div className="text-2xl font-bold">50+ yrs</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="absolute left-0 right-0 bottom-6 z-20 flex justify-center">
          <div className="flex space-x-2 bg-black/20 backdrop-blur-sm px-4 py-2 rounded-full">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                  currentSlide === index
                    ? 'bg-white w-6'
                    : 'bg-white/50 hover:bg-white/75'
                }`}
              />
            ))}
          </div>
        </div>
      </section>

      <PassionLedUsHere />

      <OurSolutions />

      <section className="py-12 md:py-20 bg-white relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">Why Choose International Responder Systems?</h2>
            <p className="text-lg md:text-xl text-gray-600">Leading the way in healthcare innovation and efficiency</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            {features.map((feature, index) => (
              <div
                key={index}
                className="text-center transform hover:scale-105 transition-all duration-300 bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-sm hover:shadow-md"
                style={{
                  opacity: 0,
                  animation: `fadeIn 0.5s ease-out forwards ${index * 0.2}s`
                }}
              >
                <div className="inline-block p-4 bg-blue-100 rounded-full mb-4">
                  <feature.icon className="h-6 w-6 md:h-8 md:w-8 text-blue-600" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold mb-3 md:mb-4">{feature.title}</h3>
                <p className="text-gray-600 text-sm md:text-base">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-12 md:py-20 bg-gray-50 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">Our Solutions</h2>
            <p className="text-lg md:text-xl text-gray-600">Comprehensive platforms designed for healthcare excellence</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {solutions.map((solution, index) => (
              <Link
                key={index}
                to={solution.link}
                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
                style={{
                  opacity: 0,
                  animation: `fadeIn 0.5s ease-out forwards ${index * 0.2}s`
                }}
              >
                <div className="relative h-40 md:h-48">
                  <img
                    src={solution.image}
                    alt={solution.title}
                    className="w-full h-full object-cover"
                  />
                  <div className={`absolute inset-0 bg-gradient-to-br ${solution.color} opacity-90`}></div>
                  <div className="absolute inset-0 p-6 text-white">
                    <solution.icon className="h-8 w-8 mb-4" />
                    <h3 className="text-xl font-bold mb-2">{solution.title}</h3>
                    <p className="text-sm opacity-90">{solution.description}</p>
                  </div>
                </div>
                <div className="p-4 md:p-6 bg-white">
                  <div className="flex items-center justify-between text-blue-600 font-medium">
                    <span>Learn more</span>
                    <ChevronRight className="h-5 w-5" />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      <section className="py-12 md:py-20 bg-gradient-to-br from-blue-50 to-blue-100 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">Work With Us</h2>
            <p className="text-lg md:text-xl text-gray-600">
              With a passionate and dedicated team, International Responder Systems is ready to serve you.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            <div className="bg-white p-6 md:p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <Building2 className="h-10 w-10 md:h-12 md:w-12 text-blue-600 mb-4 md:mb-6" />
              <h3 className="text-lg md:text-xl font-bold mb-3 md:mb-4">Services</h3>
              <p className="text-gray-600 text-sm md:text-base mb-4 md:mb-6">
                We support a diverse range of public and private sector client services with responsive and resilient systems.
              </p>
              <Link
                to="/solutions"
                className="text-blue-600 font-medium hover:text-blue-700 inline-flex items-center text-sm md:text-base"
              >
                Learn More
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Link>
            </div>

            <div className="bg-white p-6 md:p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <Package className="h-10 w-10 md:h-12 md:w-12 text-purple-600 mb-4 md:mb-6" />
              <h3 className="text-lg md:text-xl font-bold mb-3 md:mb-4">Products</h3>
              <p className="text-gray-600 text-sm md:text-base mb-4 md:mb-6">
                Discover our suite of innovative solutions designed for healthcare excellence.
              </p>
              <Link
                to="/products"
                className="text-purple-600 font-medium hover:text-purple-700 inline-flex items-center text-sm md:text-base"
              >
                View Products
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Link>
            </div>

            <div className="bg-white p-6 md:p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <Briefcase className="h-10 w-10 md:h-12 md:w-12 text-green-600 mb-4 md:mb-6" />
              <h3 className="text-lg md:text-xl font-bold mb-3 md:mb-4">Careers</h3>
              <p className="text-gray-600 text-sm md:text-base mb-4 md:mb-6">
                Join our team and make a difference in healthcare emergency response.
              </p>
              <Link
                to="/careers"
                className="text-green-600 font-medium hover:text-green-700 inline-flex items-center text-sm md:text-base"
              >
                View Opportunities
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default Home;
