import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
// Removed import: import { createSupabaseAdminClient } from '../_shared/supabaseAdmin.ts'
import Stripe from 'https://esm.sh/stripe@11.1.0?target=deno&deno-std=0.132.0' // Use compatible Stripe version
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2' // Add Supabase client import

// Define the Admin Client creation function directly in this file
const createSupabaseAdminClient = () => {
  const supabaseUrl = Deno.env.get('SUPABASE_URL')
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Missing Supabase environment variables (URL or Service Role Key)')
    // Throwing an error here is safer to prevent the function from proceeding without proper auth
    throw new Error('Missing Supabase environment variables (URL or Service Role Key)')
  }

  // Create and return the Supabase client
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  })
}


// Initialize Stripe
const stripe = Stripe(Deno.env.get('STRIPE_SECRET_KEY')!, {
  // @ts-ignore // Deno compatibility
  httpClient: Stripe.createFetchHttpClient(),
  apiVersion: '2023-10-16', // Use your desired API version
})

// Get the webhook signing secret from environment variables
const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SIGNING_SECRET_INVOICE')
if (!webhookSecret) {
  console.error("Missing environment variable STRIPE_WEBHOOK_SIGNING_SECRET_INVOICE")
  // Optionally throw an error to prevent the function from starting
}

serve(async (req: Request) => {
  const signature = req.headers.get('Stripe-Signature')
  if (!signature) {
    console.error("Missing Stripe-Signature header")
    return new Response('Missing Stripe-Signature header', { status: 400 })
  }

  const body = await req.text()

  try {
    // Verify the webhook signature
    const event = await stripe.webhooks.constructEventAsync(
      body,
      signature,
      webhookSecret! // Non-null assertion as we checked above
    )

    console.log(`Received Stripe event: ${event.type}`, event.id)

    const invoice = event.data.object as Stripe.Invoice;
    const supabaseAdmin = createSupabaseAdminClient(); // Call the locally defined function

    // Handle the event type
    switch (event.type) {
      case 'invoice.paid':
      case 'invoice.payment_succeeded': // Often redundant with invoice.paid but good to handle
        console.log(`Handling ${event.type} for invoice: ${invoice.id}`)
        await updateInvoiceStatus(supabaseAdmin, invoice);
        break;
      case 'invoice.payment_failed':
        console.log(`Handling ${event.type} for invoice: ${invoice.id}`)
        // Update status, maybe notify admin/user
        await updateInvoiceStatus(supabaseAdmin, invoice);
        break;
      case 'invoice.finalized':
        console.log(`Handling ${event.type} for invoice: ${invoice.id}`)
        // Invoice is finalized and ready for payment (or already paid if auto-advance was used)
        await updateInvoiceStatus(supabaseAdmin, invoice);
        break;
      case 'invoice.voided':
        console.log(`Handling ${event.type} for invoice: ${invoice.id}`)
        await updateInvoiceStatus(supabaseAdmin, invoice);
        break;
      case 'invoice.marked_uncollectible':
        console.log(`Handling ${event.type} for invoice: ${invoice.id}`)
        await updateInvoiceStatus(supabaseAdmin, invoice);
        break;
      case 'invoice.deleted':
         console.log(`Handling ${event.type} for invoice: ${invoice.id}`)
         // You might want to delete or mark the invoice as deleted in your DB
         const { error: deleteError } = await supabaseAdmin // Use the created client
            .from('invoices')
            .delete()
            .match({ stripe_invoice_id: invoice.id });
         if (deleteError) throw deleteError;
         console.log(`Invoice ${invoice.id} deleted from DB.`);
         break;
      // Add other invoice event types you want to handle:
      // invoice.created, invoice.sent, invoice.updated, etc.
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    // Return a 200 response to acknowledge receipt of the event
    return new Response(JSON.stringify({ received: true }), { status: 200 })

  } catch (err) {
    console.error('Webhook Error:', err.message)
    return new Response(`Webhook Error: ${err.message}`, { status: 400 })
  }
})

// Helper function to update invoice in Supabase
// Ensure supabaseAdmin parameter type matches the return type of createClient if using strict typing
async function updateInvoiceStatus(supabaseAdmin: any, invoice: Stripe.Invoice) {
  const { data, error } = await supabaseAdmin
    .from('invoices')
    .update({
      status: invoice.status,
      amount_due: invoice.amount_due,
      amount_paid: invoice.amount_paid,
      amount_remaining: invoice.amount_remaining,
      due_date: invoice.due_date ? new Date(invoice.due_date * 1000).toISOString() : null,
      invoice_pdf: invoice.invoice_pdf,
      hosted_invoice_url: invoice.hosted_invoice_url,
      updated_at: new Date().toISOString(),
      // Update any other relevant fields based on the invoice object
    })
    .match({ stripe_invoice_id: invoice.id }) // Match using the Stripe ID
    .select() // Optionally select to confirm update

  if (error) {
    console.error(`Supabase DB Error updating invoice ${invoice.id}:`, error)
    throw new Error(`Failed to update invoice ${invoice.id} in database: ${error.message}`)
  }
  if (!data || data.length === 0) {
     console.warn(`No invoice found in DB matching Stripe ID: ${invoice.id} for update.`);
     // Decide if you need to insert it here if it's missing (e.g., for invoice.created event)
  } else {
      console.log(`Invoice ${invoice.id} updated successfully in DB. New status: ${invoice.status}`)
  }
}

// Removed the commented-out shared client/cors examples as they are not needed here