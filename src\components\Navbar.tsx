import React, { useState, useRef, useEffect } from 'react';
import { Menu, X, ShoppingCart, UserCircle, ChevronDown, Library, BookOpen, Video, FileText, FilePieChart as FileChart, Calendar, Building2, Newspaper, Users, Briefcase, Package, HelpCircle, MessageSquare, Phone, Mail, Search, Download } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { useCartStore } from '../store/cartStore';
import { supabase } from '../lib/supabase';
import irsLogo from '../assets/images/new-irs-logo2.3.png';

export default function Navbar({ site_settings }) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showSearch, setShowSearch] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const user = useAuthStore((state) => state.user);
  const cartItems = useCartStore((state) => state.items);
  const navigate = useNavigate();
  const [role, setRole] = useState<string | null>(null);

  const cartItemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

  useEffect(() => {
    const checkAdminStatus = async () => {
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user?.id)
        .single();

      setRole(profile?.role);
    };
    checkAdminStatus();

    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearch(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [user]);

  // Force re-render of cart count when cart items change
  useEffect(() => {
    // This effect ensures the cart count is always up-to-date
  }, [cartItems]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.length < 2) {
      setSearchResults([]);
      return;
    }

    try {
      // Search in pages
      const { data: pages } = await supabase
        .from('pages')
        .select('title, slug')
        .ilike('title', `%${query}%`)
        .limit(3);

      // Search in blog posts
      const { data: posts } = await supabase
        .from('blog_posts')
        .select('title, id')
        .ilike('title', `%${query}%`)
        .eq('status', 'published')
        .limit(3);

      // Search in products
      const { data: products } = await supabase
        .from('products')
        .select('name, id')
        .ilike('name', `%${query}%`)
        .limit(3);

      const { data: teams } = await supabase
        .from('teams')
        .select('name, id, role')
        .or(`name.ilike.%${query}%,role.ilike.%${query}%`)
        .limit(3);

      setSearchResults([
        ...(pages || []).map(page => ({ ...page, type: 'page' })),
        ...(posts || []).map(post => ({ ...post, type: 'post' })),
        ...(products || []).map(product => ({ ...product, type: 'product' })),
        ...(teams || []).map(team => ({ ...team, type: 'team' }))
      ]);
    } catch (error) {
      console.error('Search error:', error);
    }
  };

  const handleSearchItemClick = (item: any) => {
    setShowSearch(false);
    setSearchQuery('');
    setSearchResults([]);

    switch (item.type) {
      case 'page':
        navigate(`/${item.slug}`);
        break;
      case 'post':
        navigate(`/blog`);
        break;
      case 'product':
        navigate(`/products`);
        break;
      case 'team':
        navigate(`/leadership`);
        break;
    }
  };

  const toggleDropdown = (dropdown: string) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const solutionsLinks = [
    { name: 'Overview', path: '/solutions' },
    { name: 'Products', path: '/products' },
    { name: 'GrantReady™', path: '/solutions/grantready' },
    { name: 'SOAR', path: '/solutions/soar' },
    { name: 'ELENOR', path: '/solutions/elenor' }
  ];

  const resourcesLinks = [
    { icon: Library, name: 'Resources Library', path: '/resources' },
    ...(site_settings?.features.blogEnabled ? [{ icon: BookOpen, name: 'Blog', path: '/blog' }] : []),
    { icon: Video, name: 'Webinars', path: '/webinars' },
    { icon: Download, name: 'Whitepapers', path: '/whitepapers' },
    { icon: FileText, name: 'Guides', path: '/guides' },
    { icon: FileChart, name: 'Case Studies', path: '/case-studies' },
    { icon: Calendar, name: 'Events', path: '/events' }
  ];

  const companyLinks = [
    { icon: Building2, name: 'About Us', path: '/about' },
    { icon: Users, name: 'Leadership Team', path: '/leadership' },
    { icon: Newspaper, name: 'News', path: '/news' },
    { icon: Briefcase, name: 'Careers', path: '/careers' }
  ];

  const supportLinks = [
    { icon: MessageSquare, name: 'Support Center', path: '/support' },
    { icon: HelpCircle, name: 'FAQ', path: '/faq' },
    { icon: Phone, name: 'Contact Sales', path: '/contact' },
    { icon: Mail, name: 'Email Support', href: 'mailto:<EMAIL>' }
  ];

  return (
    <nav className="bg-white shadow-lg fixed w-full z-50">
      <div className="max-w-7xl mx-auto md:px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <img
                src={irsLogo}
                alt="International Responder Systems"
                className="h-14 md:w-auto w-64"
              />
            </Link>
          </div>

          <div className="hidden md:flex items-center space-x-4">
            <Link to="/" className="text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm font-medium">
              Home
            </Link>

            {/* Solutions Dropdown */}
            <div className="relative">
              <button
                onClick={() => toggleDropdown('solutions')}
                className={`flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm font-medium ${activeDropdown === 'solutions' ? 'text-[#C42E0E]' : ''}`}
              >
                Solutions
                <ChevronDown className={`ml-1 h-4 w-4 transform transition-transform duration-200 ${activeDropdown === 'solutions' ? 'rotate-180' : ''}`} />
              </button>

              {activeDropdown === 'solutions' && (
                <div className="absolute left-0 mt-1 w-48 bg-white rounded-lg shadow-lg py-2 animate-fade-in-down">
                  {solutionsLinks.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className="block px-4 py-3 text-sm text-[#131D47] hover:bg-gray-50 hover:text-[#C42E0E] transition-colors"
                      onClick={() => setActiveDropdown(null)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Resources Dropdown */}
            <div className="relative">
              <button
                onClick={() => toggleDropdown('resources')}
                className={`flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm font-medium ${activeDropdown === 'resources' ? 'text-[#C42E0E]' : ''}`}
              >
                Resources
                <ChevronDown className={`ml-1 h-4 w-4 transform transition-transform duration-200 ${activeDropdown === 'resources' ? 'rotate-180' : ''}`} />
              </button>

              {activeDropdown === 'resources' && (
                <div className="absolute left-0 mt-1 w-64 bg-white rounded-lg shadow-lg py-2 animate-fade-in-down">
                  {resourcesLinks.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className="flex items-center px-4 py-3 text-sm text-[#131D47] hover:bg-gray-50 hover:text-[#C42E0E] transition-colors"
                      onClick={() => setActiveDropdown(null)}
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      {item.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Company Dropdown */}
            <div className="relative">
              <button
                onClick={() => toggleDropdown('company')}
                className={`flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm font-medium ${activeDropdown === 'company' ? 'text-[#C42E0E]' : ''}`}
              >
                Company
                <ChevronDown className={`ml-1 h-4 w-4 transform transition-transform duration-200 ${activeDropdown === 'company' ? 'rotate-180' : ''}`} />
              </button>

              {activeDropdown === 'company' && (
                <div className="absolute left-0 mt-1 w-64 bg-white rounded-lg shadow-lg py-2 animate-fade-in-down">
                  {companyLinks.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className="flex items-center px-4 py-3 text-sm text-[#131D47] hover:bg-gray-50 hover:text-[#C42E0E] transition-colors"
                      onClick={() => setActiveDropdown(null)}
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      {item.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Support Dropdown */}
            <div className="relative">
              <button
                onClick={() => toggleDropdown('support')}
                className={`flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm font-medium ${activeDropdown === 'support' ? 'text-[#C42E0E]' : ''}`}
              >
                Support
                <ChevronDown className={`ml-1 h-4 w-4 transform transition-transform duration-200 ${activeDropdown === 'support' ? 'rotate-180' : ''}`} />
              </button>

              {activeDropdown === 'support' && (
                <div className="absolute left-0 mt-1 w-64 bg-white rounded-lg shadow-lg py-2 animate-fade-in-down">
                  {supportLinks.map((item) => (
                    item.href ? (
                      <a
                        key={item.href}
                        href={item.href}
                        className="flex items-center px-4 py-3 text-sm text-[#131D47] hover:bg-gray-50 hover:text-[#C42E0E] transition-colors"
                        onClick={() => setActiveDropdown(null)}
                      >
                        <item.icon className="h-5 w-5 mr-3" />
                        {item.name}
                      </a>
                    ) : (
                      <Link
                        key={item.path}
                        to={item.path}
                        className="flex items-center px-4 py-3 text-sm text-[#131D47] hover:bg-gray-50 hover:text-[#C42E0E] transition-colors"
                        onClick={() => setActiveDropdown(null)}
                      >
                        <item.icon className="h-5 w-5 mr-3" />
                        {item.name}
                      </Link>
                    )
                  ))}
                </div>
              )}
            </div>
            {role === 'admin' && (
              <div className="relative">
                <Link to="/admin/dashboard"
                  className={`flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm font-medium ${activeDropdown === 'support' ? 'text-[#C42E0E]' : ''}`}
                >
                  Admin
                </Link>
              </div>
            )}


            {/* Search Button */}
            <button
              onClick={() => setShowSearch(!showSearch)}
              className="text-[#131D47] hover:text-[#C42E0E] px-3 py-2"
            >
              <Search className="h-5 w-5" />
            </button>

            <Link to="/cart" className="relative text-[#131D47] hover:text-[#C42E0E]">
              <ShoppingCart className="h-6 w-6" />
              {cartItemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-[#C42E0E] text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
                  {cartItemCount}
                </span>
              )}
            </Link>

            {user ? (
              <Link to="/profile" className="flex items-center text-[#131D47] hover:text-[#C42E0E]">
                {user?.user_metadata?.avatar_url ? <img
                  src={user?.user_metadata?.avatar_url}
                  alt="User Avatar"
                  className="h-6 w-6 rounded-full"
                />
                  : <UserCircle className="h-6 w-6" />}
              </Link>
            ) : (
              <div className="flex items-center space-x-4">
                <Link to="/login" className="text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-sm font-medium">
                  Sign In
                </Link>
                {site_settings?.features.userRegistrationEnabled && <Link to="/signup" className="bg-[#C42E0E] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#131D47]">
                  Sign Up
                </Link>}
              </div>
            )}
          </div>

          <div className="md:hidden flex items-center space-x-2">
            <Link to="/cart" className="relative text-[#131D47] hover:text-[#C42E0E]">
              <ShoppingCart className="h-5 w-5 md:h-6 md:w-6" />
              {cartItemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-[#C42E0E] text-white text-xs w-3 h-3 md:w-4 md:h-4 rounded-full flex items-center justify-center">
                  {cartItemCount}
                </span>
              )}
            </Link>
            <Link to="/profile" className="text-[#131D47] hover:text-[#C42E0E]">
              {user?.user_metadata?.avatar_url ? <img
                src={user?.user_metadata?.avatar_url}
                alt="User Avatar"
                className="h-5 w-5 md:h-6 md:w-6 rounded-full"
              />
                : <UserCircle className="h-5 w-5 md:h-6 md:w-6" />}
            </Link>

            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-1 rounded-md text-[#131D47] hover:text-[#C42E0E] focus:outline-none"
            >
              {isOpen ? <X className="h-5 w-5 md:h-6 md:w-6" /> : <Menu className="h-5 w-5 md:h-6 md:w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Search Overlay */}
      {showSearch && (
        <div
          ref={searchRef}
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300 ease-in-out"
          style={{ top: '64px' }} // Match navbar height
        >
          <div className="max-w-3xl mx-auto p-4 animate-fade-in-down">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search everything..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-4 text-lg border-0 rounded-lg focus:ring-2 focus:ring-[#C42E0E] focus:border-transparent"
                autoFocus
              />
              <button
                onClick={() => setShowSearch(false)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {searchResults.length > 0 && (
              <div className="mt-2 bg-white rounded-lg shadow-lg overflow-hidden">
                {searchResults.map((result, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearchItemClick(result)}
                    className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center transition-colors duration-150"
                  >
                    {result.type === 'page' && <FileText className="h-5 w-5 text-gray-400 mr-3" />}
                    {result.type === 'post' && <BookOpen className="h-5 w-5 text-gray-400 mr-3" />}
                    {result.type === 'product' && <Package className="h-5 w-5 text-gray-400 mr-3" />}
                    <div>
                      <div className="font-medium text-gray-800">{result.title || result.name}</div>
                      <div className="text-sm text-gray-500 capitalize">{result.type}</div>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {searchQuery.length >= 2 && searchResults.length === 0 && (
              <div className="mt-4 bg-white rounded-lg shadow-lg p-4 text-center text-gray-500">
                No results found for "{searchQuery}"
              </div>
            )}
          </div>
        </div>
      )}

      {/* Mobile Menu */}
      {isOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link to="/" className="block text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium">
              Home
            </Link>

            {/* Mobile Solutions Links */}
            {solutionsLinks.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className="block text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                {item.name}
              </Link>
            ))}

            {/* Mobile Resources Links */}
            {resourcesLinks.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className="flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.name}
              </Link>
            ))}

            {/* Mobile Company Links */}
            {companyLinks.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className="flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.name}
              </Link>
            ))}

            {/* Mobile Support Links */}
            {supportLinks.map((item) => (
              item.href ? (
                <a
                  key={item.href}
                  href={item.href}
                  className="flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium"
                >
                  <item.icon className="h-5 w-5 mr-3" />
                  {item.name}
                </a>
              ) : (
                <Link
                  key={item.path}
                  to={item.path}
                  className="flex items-center text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsOpen(false)}
                >
                  <item.icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              )
            ))}

            {user ? (
              <Link to="/profile" className="block text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium">
                My Account
              </Link>
            ) : (
              <>
                <Link to="/login" className="block text-[#131D47] hover:text-[#C42E0E] px-3 py-2 rounded-md text-base font-medium">
                  Sign In
                </Link>
                {site_settings?.features.userRegistrationEnabled
                  && (
                    <Link to="/signup" className="block w-full text-left bg-[#C42E0E] text-white px-4 py-2 rounded-md text-base font-medium hover:bg-[#131D47]">
                      Sign Up
                    </Link>
                  )}
              </>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}