name: Deploy ztechEngineering to the PROD WITH CART Environment

on:
  push:
    branches: [v2prod]

jobs:
  build:
    runs-on: ["self-hosted", "IRS"]

    steps:
      - name: Clean workspace
        run: |
          # Clean the workspace with proper permissions
          sudo rm -rf ./* || true
          sudo rm -rf ./.* || true

      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Check for changes in node_modules
        id: check_node_modules
        run: |
          echo "Checking for changes in node_modules..."
          if git diff --quiet HEAD^ HEAD -- node_modules/; then
            echo "No changes in node_modules"
            echo "install_node_modules=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected in node_modules"
            echo "install_node_modules=true" >> $GITHUB_OUTPUT
          fi

      - name: Install frontend modules
        if: steps.check_node_modules.outputs.install_node_modules == 'true'
        run: |
          echo "Installing frontend modules..."
          npm i
      # - name: Write .env for backend
      #   run: |
      #     touch .env
      #     echo "${{ secrets.IRS_ENV_FILE_CONTENT }}" > .env
      #     chmod 600 .env
      #     ls -la .env

      - name: Build the frontend
        run: |
          npm run build

      # - name: Check for changes in Supabase functions
      #   id: check_functions
      #   run: |
      #     echo "Checking for changes in supabase/functions..."
      #     if git diff --quiet HEAD^ HEAD -- supabase/functions/; then
      #       echo "No changes in functions"
      #       echo "deploy=false" >> $GITHUB_OUTPUT
      #     else
      #       echo "Changes detected in functions"
      #       echo "deploy=true" >> $GITHUB_OUTPUT
      #     fi

      # - name: Copy Supabase Edge Functions into volume
      #   if: steps.check_functions.outputs.deploy == 'true'
      #   run: |
      #    echo "Copying Supabase Edge Functions to Docker volume..."
      #     sudo cp -r ./supabase/functions/* /root/supabase/docker/volumes/functions/
      #     echo "Done copying functions."

      # - name: Restart Supabase Edge Functions container
      #   run: |
      #    echo "Is there a change in node_modules?"
      #     echo steps.check_node_modules.outputs.install_node_modules
          
      #     echo "Is there a change in functions?"
      #     echo steps.check_functions.outputs.deploy
        # if: steps.check_functions.outputs.deploy == 'true'
        # run: |
        #   sudo -i
        #   cd /root/supabase/docker/
        #   docker compose restart functions
