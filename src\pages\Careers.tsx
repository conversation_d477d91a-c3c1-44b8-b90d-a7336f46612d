import React, { useState, useEffect } from 'react';
import { Briefcase, Users, Target, Heart, Zap, Star, Award, Mail, Building2, CheckCircle, Coffee, Brain, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';

interface JobListing {
  id: string;
  title: string;
  department: string;
  location: string;
  type: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary_range: {
    min: number;
    max: number;
    currency: string;
  } | null;
}

export default function Careers() {
  const [jobs, setJobs] = useState<JobListing[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchJobs();
  }, []);

  const fetchJobs = async () => {
    try {
      const { data } = await supabase
        .from('job_listings')
        .select('*')
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      setJobs(data || []);
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const values = [
    { icon: Target, title: 'Mission', description: 'Revolutionize health & emergency response' },
    { icon: Heart, title: 'Vision', description: 'Redefining public health standards' },
    { icon: Zap, title: 'Impact', description: 'Making a difference in healthcare' }
  ];

  const beliefs = [
    { icon: CheckCircle, title: 'Pursue Perfection', description: 'Encourage questions, feedback and discourse to fuel individual growth' },
    { icon: Heart, title: 'Express Gratitude', description: 'Go the extra mile. Show appreciation to employees and clients' },
    { icon: Coffee, title: 'Employee Satisfaction', description: 'Happy employees make happier customers' },
    { icon: Star, title: 'Cultivate Success', description: 'Push the boundaries on what is possible' },
    { icon: Brain, title: 'Value Expertise', description: 'Knowledgeable people give us our competitive edge' },
    { icon: Target, title: 'Push boundaries', description: 'Set high standards and continually raise the bar' }
  ];

  const benefits = [
    { icon: Building2, title: '401k', description: 'Comprehensive retirement plans' },
    { icon: Heart, title: 'Medical', description: 'Full health coverage' },
    { icon: Coffee, title: 'PTO', description: 'Flexible time off' },
    { icon: Brain, title: 'Growth', description: 'Career development opportunities' }
  ];

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-600 to-blue-800 text-white py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-6">Join Our Mission</h1>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Be part of a team that's revolutionizing healthcare emergency response. We're looking for passionate individuals who want to make a real difference.
            </p>
            <a 
              href="#openings"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300"
            >
              View Open Positions
            </a>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-white" style={{ clipPath: 'polygon(0 100%, 100% 100%, 100% 0)' }}></div>
      </section>

      {/* Current Openings Section */}
      <section id="openings" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Current Openings</h2>
            <p className="text-xl text-gray-600">Join our growing team</p>
          </div>

          {loading ? (
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : jobs.length > 0 ? (
            <div className="grid md:grid-cols-2 gap-8">
              {jobs.map((job) => (
                <div key={job.id} className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 hover:border-blue-500 transition-colors">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <Briefcase className="h-6 w-6 text-blue-600 mr-3" />
                        <h3 className="text-xl font-bold">{job.title}</h3>
                      </div>
                      <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                        {job.type}
                      </span>
                    </div>
                    
                    <div className="space-y-4 mb-6">
                      <div className="flex items-center text-gray-600">
                        <Building2 className="h-5 w-5 mr-2" />
                        <span>{job.department}</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Users className="h-5 w-5 mr-2" />
                        <span>{job.location}</span>
                      </div>
                      {job.salary_range && (
                        <div className="flex items-center text-gray-600">
                          <Award className="h-5 w-5 mr-2" />
                          <span>
                            {job.salary_range.currency} {job.salary_range.min.toLocaleString()} - {job.salary_range.max.toLocaleString()} / year
                          </span>
                        </div>
                      )}
                    </div>

                    <p className="text-gray-600 mb-6">
                      {job.description.length > 200 
                        ? `${job.description.substring(0, 200)}...` 
                        : job.description}
                    </p>

                    <div className="flex gap-3">
                      <Link
                        to={`/careers/job/${job.id}`}
                        className="inline-flex items-center bg-gray-100 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300"
                      >
                        View Details
                      </Link>
                      <Link
                        to={`/careers/apply/${job.id}`}
                        className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
                      >
                        Apply Now
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <Briefcase className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600 mb-6">
                We're always looking for talented individuals to join our team. 
                Check back soon for new opportunities or send us your resume.
              </p>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
              >
                <Mail className="mr-2 h-5 w-5" />
                Send Your Resume
              </a>
            </div>
          )}
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Our Values</h2>
            <p className="text-xl text-gray-600">What drives us forward</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white p-8 rounded-lg text-center shadow-lg">
                <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-6">
                  <value.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-4">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Core Beliefs Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Core Beliefs</h2>
            <p className="text-xl text-gray-600">
              Our core beliefs drive everything we do
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {beliefs.map((belief, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-lg shadow-lg">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <belief.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold">{belief.title}</h3>
                </div>
                <p className="text-gray-600">{belief.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Benefits</h2>
            <p className="text-xl text-gray-600">What we offer to our team</p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="bg-blue-100 p-4 rounded-full w-fit mx-auto mb-6">
                  <benefit.icon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stay Connected Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-8">Stay Connected</h2>
          <div className="flex justify-center space-x-4">
            <a
              href="https://www.linkedin.com/company/international-responder-systems"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300"
            >
              Follow us on LinkedIn
            </a>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white/10 transition duration-300"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}