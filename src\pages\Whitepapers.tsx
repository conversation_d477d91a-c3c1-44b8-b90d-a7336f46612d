import { useState, useEffect, useCallback } from 'react';
import { FileText, Search, Download, Calendar, User, ExternalLink } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import LoginModal from '../components/LoginModal';

interface Whitepaper {
  id: string;
  title: string;
  description: string;
  pdf_url: string;
  image_url: string | null;
  is_public: boolean;
  download_count: number;
  type: string;
  whitepaper_link?: string;
  created_at: string;
}

export default function Whitepapers() {
  const [whitepapers, setWhitepapers] = useState<Whitepaper[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [downloadingId, setDownloadingId] = useState<string | null>(null);
  const [filteredWhitepapers, setFilteredWhitepapers] = useState<Whitepaper[]>([]);
  const [expandedDescriptions, setExpandedDescriptions] = useState<Record<string, boolean>>({});
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState<{ id: string, url: string } | null>(null);
  const user = useAuthStore((state) => state.user);

  const fetchWhitepapers = useCallback(async () => {
    try {
      let query = supabase
        .from('guides')
        .select('*')
        .eq('type', 'whitepaper')
        .order('created_at', { ascending: false });

      // Only show public whitepapers to non-authenticated users
      if (!user) {
        query = query.eq('is_public', true);
      }

      const { data, error } = await query;

      if (error) throw error;
      setWhitepapers(data || []);
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchWhitepapers();
  }, [fetchWhitepapers]);

  useEffect(() => {
    // Filter whitepapers based on search query
    const filtered = whitepapers.filter(whitepaper => {
      const query = searchQuery.toLowerCase();
      return (
        whitepaper.title.toLowerCase().includes(query) ||
        whitepaper.description.toLowerCase().includes(query)
      );
    });

    setFilteredWhitepapers(filtered);
  }, [searchQuery, whitepapers]);

  // Function to toggle description expansion
  const toggleDescription = (whitepaperID: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [whitepaperID]: !prev[whitepaperID]
    }));
  };

  const handleDownload = useCallback(async (guideId: string, pdfUrl: string) => {
    // Check if user is authenticated
    if (!user) {
      // Show login modal
      setSelectedPdf({ id: guideId, url: pdfUrl });
      // Store the URL type to determine which title to show
      sessionStorage.setItem('loginModalType', 'download');
      setShowLoginModal(true);
      return;
    }

    try {
      setDownloadingId(guideId);

      // First get current download count
      const { data: guideData, error: fetchError } = await supabase
        .from('guides')
        .select('download_count')
        .eq('id', guideId)
        .single();

      if (fetchError) throw fetchError;

      // Increment download count
      const { error: updateError } = await supabase
        .from('guides')
        .update({ download_count: (guideData.download_count || 0) + 1 })
        .eq('id', guideId);

      if (updateError) throw updateError;

      // Handle download
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = pdfUrl.split('/').pop() || 'whitepaper.pdf';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Refresh whitepapers to show updated count
      fetchWhitepapers();
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || 'Failed to download whitepaper');
    } finally {
      setDownloadingId(null);
    }
  }, [user, fetchWhitepapers]);

  // Handle closing the login modal
  const handleCloseLoginModal = useCallback(() => {
    setShowLoginModal(false);
    setSelectedPdf(null);
    // Clear the modal type from session storage
    sessionStorage.removeItem('loginModalType');
  }, []);

  // Handle continuing with download or view online after login
  const handleContinueDownload = useCallback(() => {
    if (selectedPdf && user) {
      // Check if the URL is a PDF (likely a download) or a regular URL (likely view online)
      if (selectedPdf.url.toLowerCase().endsWith('.pdf')) {
        handleDownload(selectedPdf.id, selectedPdf.url);
      } else {
        // Open in new tab for non-PDF URLs (view online)
        window.open(selectedPdf.url, '_blank', 'noopener,noreferrer');
      }
      setSelectedPdf(null);
    }
  }, [selectedPdf, user, handleDownload]);

  // Check if user changed and we have a pending download
  useEffect(() => {
    if (user && selectedPdf) {
      handleContinueDownload();
    }
  }, [user, selectedPdf, handleContinueDownload]);

  // Function to truncate text
  const truncateText = (text: string, wordCount: number = 15) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordCount) return text;
    return words.slice(0, wordCount).join(' ') + '...';
  };

  return (
    <div className="pt-16">
      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={handleCloseLoginModal}
        title="Download Whitepaper"
        message="Please log in to download this whitepaper. Logging in allows us to save your information for future downloads."
      />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-left">
            <h1 className="text-4xl font-bold mb-6">Whitepapers</h1>
            <p className="text-xl mb-10 text-blue-100">
              Explore our collection of whitepapers covering industry insights, best practices, and innovative approaches to healthcare and emergency management.
            </p>
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search whitepapers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Whitepapers Grid */}
      {!loading && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold mb-8">Featured Whitepapers</h2>

            {error ? (
              <div className="bg-red-50 text-red-600 p-6 rounded-lg text-center">
                <p>Error loading whitepapers: {error}</p>
              </div>
            ) : filteredWhitepapers.length === 0 ? (
              searchQuery ? (
                <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                  <p className="text-gray-600">
                    No whitepapers match your search "{searchQuery}". Try different keywords.
                  </p>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">No whitepapers available</h3>
                  <p className="text-gray-500">Check back soon for new whitepapers.</p>
                </div>
              )
            ) : (
              <div className="grid md:grid-cols-3 gap-8">
                {filteredWhitepapers.map((whitepaper) => (
                  <div key={whitepaper.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    {whitepaper.image_url ? (
                      <img
                        src={whitepaper.image_url}
                        alt={whitepaper.title}
                        className="w-full h-48 object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.style.display = 'none';
                          target.parentElement!.innerHTML = `
                            <div class="w-full h-48 bg-gray-100 flex items-center justify-center">
                              <div class="text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                            </div>
                          `;
                        }}
                      />
                    ) : (
                      <div className="w-full h-48 bg-gray-100 flex items-center justify-center">
                        <div className="text-center">
                          <FileText className="h-12 w-12 text-gray-400 mx-auto" />
                        </div>
                      </div>
                    )}

                    <div className="p-5">
                      <h3 className="text-xl font-bold mb-2 line-clamp-2">{whitepaper.title}</h3>
                      <div className="text-gray-600 mb-4 text-sm">
                        {expandedDescriptions[whitepaper.id] ? (
                          <>
                            <p className="break-words">{whitepaper.description}</p>
                            <button
                              onClick={() => toggleDescription(whitepaper.id)}
                              className="text-blue-600 text-sm mt-1 hover:underline"
                            >
                              See Less
                            </button>
                          </>
                        ) : (
                          <>
                            <p className="break-words">{truncateText(whitepaper.description)}</p>
                            {whitepaper.description && whitepaper.description.split(' ').length > 15 && (
                              <button
                                onClick={() => toggleDescription(whitepaper.id)}
                                className="text-blue-600 text-sm mt-1 hover:underline"
                              >
                                See More
                              </button>
                            )}
                          </>
                        )}
                      </div>

                      <div className="flex items-center text-sm text-gray-500 mb-4">
                        <Calendar className="h-4 w-4 mr-1.5" />
                        <span>{new Date(whitepaper.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}</span>
                        <span className="mx-2 text-gray-300">•</span>
                        <User className="h-4 w-4 mr-1.5" />
                        <span>IRS Team</span>
                      </div>

                      <div className="flex flex-col space-y-3 mt-4 pt-3 border-t border-gray-100">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-gray-500">
                            <Download className="h-4 w-4 mr-1" />
                            <span>{whitepaper.download_count} downloads</span>
                          </div>
                          <button
                            onClick={() => handleDownload(whitepaper.id, whitepaper.pdf_url)}
                            className={`flex items-center text-blue-600 font-medium hover:text-blue-700 ${downloadingId === whitepaper.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                            disabled={downloadingId === whitepaper.id}
                          >
                            {downloadingId === whitepaper.id ? 'Downloading...' : 'Download PDF'}
                            <FileText className="h-4 w-4 ml-2" />
                          </button>
                        </div>

                        {whitepaper.whitepaper_link && (
                          <button
                            onClick={() => {
                              if (!user) {
                                // Show login modal with different title for online content
                                setSelectedPdf({ id: whitepaper.id, url: whitepaper.whitepaper_link || '' });
                                // Store the URL type to determine which title to show
                                sessionStorage.setItem('loginModalType', 'viewOnline');
                                setShowLoginModal(true);
                              } else {
                                // Open in new tab
                                window.open(whitepaper.whitepaper_link, '_blank', 'noopener,noreferrer');
                              }
                            }}
                            className="flex items-center justify-center w-full py-2 bg-blue-500 text-white rounded-md font-medium hover:bg-blue-600 transition-colors"
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            View Online
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Call to Action */}
      {!loading && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-6">Need More Information?</h2>
            <p className="text-lg text-gray-600 mb-8">
              Contact our team to request additional resources or to discuss how our solutions can help your organization.
            </p>
            <a
              href="/contact"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Contact Us
              <ExternalLink className="h-4 w-4 ml-2" />
            </a>
          </div>
        </section>
      )}
    </div>
  );
}
