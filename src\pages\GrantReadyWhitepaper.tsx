import React from 'react';
import { Mail, FileText, Building2, BarChart, Network, Lock, Zap, ArrowRight, ExternalLink } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function GrantReadyWhitepaper() {
  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-600 to-blue-800 text-white py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl">
            <span className="inline-block px-4 py-1 bg-blue-500/20 backdrop-blur-sm rounded-full text-sm font-medium mb-6">
              Whitepaper
            </span>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Innovative Approach to Grant Lifecycle Efficiency</h1>
            <p className="text-xl text-blue-100 mb-8">
              Transforming public health data systems through modern solutions
            </p>
            <Link
              to="/contact"
              className="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300"
            >
              <Mail className="mr-2 h-5 w-5" />
              Contact Us
            </Link>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-white" style={{ clipPath: 'polygon(0 100%, 100% 100%, 100% 0)' }}></div>
      </section>

      {/* Content Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="prose prose-lg max-w-none">
            {/* Problem Statement */}
            <div className="bg-white rounded-lg shadow-xl p-8 mb-12">
              <div className="flex items-center mb-6">
                <div className="bg-red-100 p-3 rounded-full mr-4">
                  <BarChart className="h-6 w-6 text-red-600" />
                </div>
                <h2 className="text-2xl font-bold m-0">Problem Statement</h2>
              </div>
              <div className="grid md:grid-cols-2 gap-8 mb-8">
                <img
                  src="https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                  alt="Healthcare data systems"
                  className="rounded-lg shadow-md"
                />
                <div>
                  <p className="text-gray-600">
                    Public health data systems are critical sources of actionable intelligence used
                    by federal, state, tribal, local, and territorial public health agencies to protect Americans against infectious and non-infectious health threats.
                  </p>
                  <div className="mt-4 flex items-center text-red-600">
                    <Lock className="h-5 w-5 mr-2" />
                    <span className="font-medium">Current systems are antiquated and siloed</span>
                  </div>
                </div>
              </div>
              <p className="text-gray-600 mb-6">
                However, the nation's public health data systems are antiquated, siloed, chronically underfunded, and rely on older surveillance methods, leading to delayed detection and response. The COVID-19 pandemic demonstrated a need to significantly improve the collection and use of critical health data at all levels of government while reducing the burden placed on those who provide the data.
              </p>
            </div>

            {/* Solution Section */}
            <div className="bg-white rounded-lg shadow-xl p-8 mb-12">
              <div className="flex items-center mb-6">
                <div className="bg-green-100 p-3 rounded-full mr-4">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold m-0">Solution</h2>
              </div>
              <div className="grid md:grid-cols-2 gap-8 items-center mb-8">
                <div>
                  <p className="text-gray-600">
                    According to CDC's Data Modernization Initiative (DMI) the nation will move
                    from siloed and brittle public health data systems to connected, resilient,
                    adaptable, and sustainable response-ready systems.
                  </p>
                  <div className="mt-6 space-y-4">
                    <div className="flex items-center text-green-600">
                      <Network className="h-5 w-5 mr-2" />
                      <span className="font-medium">Connected & Resilient Systems</span>
                    </div>
                    <div className="flex items-center text-green-600">
                      <Building2 className="h-5 w-5 mr-2" />
                      <span className="font-medium">Sustainable Infrastructure</span>
                    </div>
                  </div>
                </div>
                <img
                  src="https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                  alt="Modern healthcare solutions"
                  className="rounded-lg shadow-md"
                />
              </div>
            </div>

            {/* Priorities Section */}
            <div className="space-y-8 mb-12">
              <div className="bg-blue-50 border-l-4 border-blue-600 p-6 rounded-r-lg">
                <h3 className="text-xl font-bold text-blue-800 mb-4">Priority 1: Build the Right Foundation</h3>
                <p className="text-gray-600">
                  GrantReady™ provides a solution to strengthen and unify critical infrastructure
                  for a response-ready ecosystem by providing a secure and scalable
                  foundation with appropriate automated data sources.
                </p>
              </div>

              <div className="bg-purple-50 border-l-4 border-purple-600 p-6 rounded-r-lg">
                <h3 className="text-xl font-bold text-purple-800 mb-4">Priority 2: Accelerate Data into Action</h3>
                <p className="text-gray-600">
                  GrantReady™ ensures that users can use data in a faster, more
                  interoperable way that provides high-quality information for real-time,
                  comprehensive decision-making.
                </p>
              </div>

              <div className="bg-green-50 border-l-4 border-green-600 p-6 rounded-r-lg">
                <h3 className="text-xl font-bold text-green-800 mb-4">Priority 4: Support and Extend Partnerships</h3>
                <p className="text-gray-600">
                  Grant Ready allows state partners to better engage with local
                  partners, ensure transparency, and quickly address policy challenges.
                </p>
              </div>
            </div>

            {/* References Section */}
            <div className="bg-gray-50 rounded-lg p-8 mb-12">
              <h2 className="text-2xl font-bold mb-6">References</h2>
              <div className="space-y-4">
                {[
                  {
                    title: "CDC Public Health Emergency Preparedness Program and Guidance",
                    url: "https://www.cdc.gov/orr/readiness/phep/index.htm"
                  },
                  {
                    title: "CDC Data Modernization Initiative",
                    url: "https://www.cdc.gov/surveillance/data-modernization/index.html"
                  },
                  {
                    title: "The Paperwork Reduction Act of 1995",
                    url: "https://www.govinfo.gov/content/pkg/PLAW-104publ13/html/PLAW-104publ13.htm"
                  },
                  {
                    title: "CDC PHEP ORR Reporting and Tracking System (PORTS)",
                    url: "https://www.cdc.gov/orr/readiness/resources/ports.htm"
                  }
                ].map((ref, index) => (
                  <a
                    key={index}
                    href={ref.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
                  >
                    <FileText className="h-5 w-5 text-blue-600 mr-3" />
                    <span className="flex-1">{ref.title}</span>
                    <ExternalLink className="h-4 w-4 text-gray-400" />
                  </a>
                ))}
              </div>
            </div>

            {/* Contact Section */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white rounded-lg p-8">
              <h2 className="text-2xl font-bold mb-6">Need more info?</h2>
              <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg">
                <div className="mb-4">
                  <h3 className="text-xl font-bold mb-2">Graciela Malave</h3>
                  <p className="text-blue-100">
                    Clinical Laboratory Professional | Master Exercise Practitioner (MEP) / NIMS Instructor
                  </p>
                </div>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors"
                >
                  <Mail className="h-4 w-4 mr-2" />
                  Contact via Email
                  <ArrowRight className="h-4 w-4 ml-2" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}