import { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import Navbar from './components/Navbar';
import ScrollToTop from './ScrollToTop';
import Home from './pages/Home';
import Solutions from './pages/Solutions';
import Products from './pages/Products';
import GrantReady from './pages/GrantReady';
import SOAR from './pages/SOAR';
import ELENOR from './pages/ELENOR';
import Contact from './pages/Contact';
import SignUp from './pages/SignUp';
import Login from './pages/Login';
import Cart from './pages/Cart';
import Profile from './pages/Profile';
import Resources from './pages/Resources';
import Blog from './pages/Blog';
import Webinars from './pages/Webinars';
import Guides from './pages/Guides';
import CaseStudies from './pages/CaseStudies';
import Events from './pages/Events';
import GrantReadyWhitepaper from './pages/GrantReadyWhitepaper';
import Whitepapers from './pages/Whitepapers';
import Terms from './pages/Terms';
import Privacy from './pages/Privacy';
import Accessibility from './pages/Accessibility';
import About from './pages/About';
import Leadership from './pages/Leadership';
import News from './pages/News';
import Careers from './pages/Careers';
import JobApplication from './pages/JobApplication';
import FAQ from './pages/FAQ';
import PacificRim from './pages/PacificRim';
import Support from './pages/Support';
import Dashboard from './pages/admin/Dashboard';
import UserManagement from './pages/admin/UserManagement';
import { supabase, checkConnection } from './lib/supabase';
// import { useAuthStore } from './store/authStore';
import { AuthProvider } from './lib/AuthContext';
import { ProtectedRoute } from './components/ProtectedRoute';
import AdminLayout from './components/AdminLayout';
import ClientLayout from './components/ClientLayout';
// Remove this duplicate import:
// import { useEffect } from 'react';
import { useAuthStore } from './store/authStore';
import { useCartStore } from './store/cartStore';
import ResetPassword from './pages/ResetPassword';
import CaseStudyDetail from './pages/CaseStudyDetail';
import BlogDetails from './pages/BlogDetails';
import NotFound from './pages/NotFound';
import MaintenancePage from './pages/MaintenancePage';
import CheckoutSuccess from './pages/checkout/Success';
import JobDetails from './pages/JobDetails';

function App() {
  const setUser = useAuthStore((state) => state.setUser);
  const user = useAuthStore((state) => state.user);
  const fetchCart = useCartStore((state) => state.fetchCart);
  const syncCart = useCartStore((state) => state.syncCart);
  const [connectionError, setConnectionError] = useState(false);
  const [siteSettings, setSiteSettings] = useState({
    general: {
      siteName: 'International Responder Systems - Healthcare Emergency Response Solutions',
      siteDescription: 'Leading provider of healthcare emergency response and grant management solutions.',
      contactEmail: '',
      phone: '',
      address: '',
      socialLinks: {
        linkedin: '',
        twitter: '',
        facebook: ''
      }
    },
    seo: {
      defaultTitle: 'International Responder Systems - Healthcare Emergency Response Solutions',
      defaultDescription: 'Leading provider of healthcare emergency response and grant management solutions.',
      defaultKeywords: 'healthcare, emergency response, grant management, SOAR, GrantReady',
      googleAnalyticsId: '',
      googleTagManagerId: '',
      googleSiteVerification: '',
      bingVerification: '',
      robotsTxt: '',
      sitemapEnabled: false
    },
    appearance: {
      logo: '',
      favicon: '/favicon.ico',
      primaryColor: '#2563eb',
      secondaryColor: '#1e40af',
      fontFamily: 'Inter'
    },
    features: {
      blogEnabled: false,
      commentsEnabled: false,
      userRegistrationEnabled: false,
      maintenanceMode: false
    }
  });

  useEffect(() => {
    // Check Supabase connection
    const checkSupabaseConnection = async () => {
      const isConnected = await checkConnection();
      setConnectionError(!isConnected);
    };

    checkSupabaseConnection();

    // Set initial user
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, [setUser]);

  // Fetch cart items whenever user changes
  useEffect(() => {
    // Fetch site settings from Supabase
    const fetchSiteSettings = async () => {
      const { data, error } = await supabase
        .from('site_settings')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;
      if (data && data.length > 0) {
        setSiteSettings(data[0].settings); // Changed from data[0] to data[0].settings
      }
    };

    fetchSiteSettings();

    if (user?.id) {
      fetchCart(user.id);

      // Set up periodic sync to keep cart updated
      const syncInterval = setInterval(() => {
        syncCart();
      }, 60000); // Sync every minute

      return () => clearInterval(syncInterval);
    }
  }, [user?.id, fetchCart, syncCart]);

  return (
    <HelmetProvider>
      <Helmet>
        <title>{siteSettings.seo.defaultTitle}</title>
        <meta name="description" content={siteSettings.seo.defaultDescription} />
        <meta name="keywords" content={siteSettings.seo.defaultKeywords} />
        <meta
          name="google-site-verification"
          content={siteSettings.seo.googleSiteVerification}
        />
        <meta name="msvalidate.01" content={siteSettings.seo.bingVerification} />
        <link rel="icon" href={siteSettings.appearance.favicon} />

        <script>
          {`
          (function(w,d,s,l,i){
            w[l]=w[l]||[];w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});
            var f=d.getElementsByTagName(s)[0], j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
            j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
            f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','${siteSettings.seo.googleTagManagerId}');
        `}
        </script>

      </Helmet>
      <AuthProvider>
        <noscript>
          <iframe
            src={`https://www.googletagmanager.com/ns.html?id=${siteSettings.seo.googleTagManagerId}`}
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
          />
        </noscript>
        {siteSettings.features.maintenanceMode
          ? (<>

            <Router>
              <Routes>
                <Route path="*" element={<MaintenancePage site_settings={siteSettings} />} />
                <Route path="/login" element={<Login site_settings={siteSettings} />} />
                <Route path="/admin" element={
                  <ProtectedRoute requireAdmin>
                    <AdminLayout />
                  </ProtectedRoute>
                }>
                  <Route path="dashboard" element={<Dashboard />} />
                </Route>
              </Routes>
            </Router>
          </>)
          : (
            <Router>
              <ScrollToTop />
              <div className="min-h-screen bg-white flex flex-col">
                {connectionError && (
                  <div className="fixed top-0 left-0 right-0 bg-red-500 text-white p-4 z-50 flex items-center justify-center">
                    <AlertCircle className="h-5 w-5 mr-2" />
                    <span>Connection error. Please try again later.</span>
                  </div>
                )}
                <Navbar site_settings={siteSettings} />
                <div className="flex-grow">
                  <Routes>
                    <Route path="/" element={<ClientLayout />}>
                      {/* <Route path="/:slug" element={<PagePreview />} /> */}
                      <Route path="/" element={<Home />} />
                      <Route path="/solutions" element={<Solutions />} />
                      <Route path="/products" element={<Products />} />
                      <Route path="/solutions/grantready" element={<GrantReady />} />
                      <Route path="/solutions/grantready/whitepaper" element={<GrantReadyWhitepaper />} />
                      <Route path="/solutions/soar" element={<SOAR />} />
                      <Route path="/solutions/elenor" element={<ELENOR />} />
                      <Route path="/pacific-rim" element={<PacificRim />} />
                      <Route path="/resources" element={<Resources />} />
                      {siteSettings.features.blogEnabled && (
                        <>
                          <Route path="/blog" element={<Blog />} />
                          <Route path="/blog/:id" element={<BlogDetails site_settings={siteSettings} />} />
                        </>
                      )}
                      <Route path="/webinars" element={<Webinars />} />
                      <Route path="/guides" element={<Guides />} />
                      <Route path="/case-studies" element={<CaseStudies />} />
                      <Route path='/case-studies/:id' element={<CaseStudyDetail />} />
                      <Route path="/events" element={<Events />} />
                      <Route path="/contact" element={<Contact />} />
                      {siteSettings.features.userRegistrationEnabled && <Route path="/signup" element={<SignUp />} />}
                      <Route path="/login" element={<Login />} />
                      <Route path='/reset-password' element={<ResetPassword />} />
                      {/* <Route path="/update-password" element={<UpdatePassword />} /> */}
                      <Route path="/cart" element={<Cart />} />
                      <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
                      <Route path="/terms" element={<Terms />} />
                      <Route path="/privacy" element={<Privacy />} />
                      <Route path="/accessibility" element={<Accessibility />} />
                      <Route path="/about" element={<About />} />
                      <Route path="/leadership" element={<Leadership />} />
                      <Route path="/news" element={<News />} />
                      <Route path="/news/:id" element={<BlogDetails />} />
                      <Route path="/careers" element={<Careers />} />
                      <Route path="/careers/job/:id" element={<JobDetails />} />
                      <Route path="/careers/apply/:id" element={<JobApplication />} />
                      <Route path="/faq" element={<FAQ />} />
                      <Route path="/support" element={<Support />} />
                      <Route path='/checkout/success' element={<CheckoutSuccess />} />
                      <Route path="/whitepapers" element={<Whitepapers />} />
                    </Route>
                    <Route path="/admin" element={
                      <ProtectedRoute requireAdmin>
                        <AdminLayout />
                      </ProtectedRoute>
                    }>
                      <Route path="dashboard" element={<Dashboard />} />
                      <Route path="users" element={<UserManagement />} />
                    </Route>
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </div>
              </div>
            </Router>
          )
        }
      </AuthProvider>
    </HelmetProvider>
  );
}

export default App;