@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes gradient {
  0% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(-30px, 30px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 3D Transforms */
.perspective-1000 {
  perspective: 1000px;
}

.transform-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

/* Smooth Scroll - disabled for ScrollToTop functionality */
/* html {
  scroll-behavior: smooth;
} */

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Slider Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 6s ease-in-out infinite;
  animation-delay: 1s;
}

/* Button Glow Effects */
.glow-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.glow-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255,255,255,0.8) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-button:hover::before {
  opacity: 0.15;
}

.glow-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.3s ease;
  transform: rotate(45deg);
}

.glow-button:hover::after {
  opacity: 0.1;
}

/* 3D Image Container */
.image-3d-container {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.image-3d {
  transform: rotateX(5deg) rotateY(-5deg);
  transition: transform 0.3s ease;
}

.image-3d:hover {
  transform: rotateX(0deg) rotateY(0deg) scale(1.05);
}

/* Cursor effects */
.cursor-glow {
  filter: blur(8px);
  mix-blend-mode: screen;
}

/* Gradient text */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, currentColor, #60a5fa);
}

/* Slide transition */
.slide-enter {
  transform: translateX(100%);
}

.slide-enter-active {
  transform: translateX(0%);
  transition: transform 500ms ease-in-out;
}

.slide-exit {
  transform: translateX(0%);
}

.slide-exit-active {
  transform: translateX(-100%);
  transition: transform 500ms ease-in-out;
}

/* Button Micro-interactions */
.micro-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
}

.micro-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: skewX(-20deg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.micro-button:hover::before {
  left: 100%;
}

.micro-button:hover {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.micro-button:active {
  transform: translateY(0);
}

.micro-button .icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.micro-button:hover .icon {
  transform: translateX(4px);
}