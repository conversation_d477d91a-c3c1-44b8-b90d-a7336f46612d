import React, { useState, useEffect } from 'react';
import { Calendar, ArrowRight, Tag, Search, ArrowUpRight, Timer as TimerIcon } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';

interface NewsPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  status: string;
  featured_image: string;
  created_at: string;
  type: string;
}

export default function News() {
  const navigate = useNavigate();
  const [newsPosts, setNewsPosts] = useState<NewsPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<NewsPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const fetchNewsPosts = async () => {
    try {
      const { data: posts, error: postsError } = await supabase
        .from('blog_posts')
        .select(`*`)
        .eq('status', 'published')
        .eq('type', 'news')
        .order('created_at', { ascending: false });

      if (postsError) throw postsError;
      return posts || [];
    } catch (err: any) {
      setError(err.message);
      return [];
    }
  };

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        const posts = await fetchNewsPosts();
        if (isMounted) {
          setNewsPosts(posts);
          setFilteredPosts(posts);
        }
      } catch (err: any) {
        if (isMounted) setError(err.message);
      } finally {
        if (isMounted) setLoading(false);
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, []);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredPosts(newsPosts);
    } else {
      const filtered = newsPosts.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredPosts(filtered);
    }
  }, [searchQuery, newsPosts]);

  if (loading) return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  if (error) return <div className="min-h-screen flex items-center justify-center text-red-500">Error: {error}</div>;

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-left">
            <h1 className="text-4xl font-bold mb-6">Latest News</h1>
            <p className="text-xl mb-10 text-blue-100">
              Stay updated with the latest developments at International Responder Systems
            </p>
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search news..."
                className="w-full pl-12 pr-4 py-3.5 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured News - Only show when not searching */}
      {filteredPosts.length > 0 && searchQuery.trim() === '' && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold mb-8">Featured News</h2>
            <div className="bg-white rounded-lg shadow-xl overflow-hidden">
              <div className="md:flex">
                {filteredPosts[0].featured_image && (
                  <div className="md:w-1/2">
                    <img
                      src={filteredPosts[0].featured_image}
                      alt={filteredPosts[0].title}
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                )}
                <div className="md:w-1/2 p-8">
                  <span className="inline-block px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mb-4">
                    News
                  </span>
                  <h2 className="text-3xl font-bold mb-4">{filteredPosts[0].title}</h2>
                  <p className="text-gray-600 mb-6">
                    {filteredPosts[0].excerpt || filteredPosts[0].content.substring(0, 200) + '...'}
                  </p>
                  <div className="flex items-center mb-6">
                    <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="text-gray-600">
                      {new Date(filteredPosts[0].created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                  <button
                    onClick={() => navigate(`/news/${filteredPosts[0].id}`)}
                    className="flex items-center gap-1 bg-blue-400 hover:bg-blue-300 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
                  >
                    Read Full Story
                    <ArrowUpRight className="h-5 w-5 ml-2" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* News Grid - Show all filtered posts when searching */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold mb-8">
            {searchQuery.trim() ? 'Search Results' : 'Recent News'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.length > 0 ? (
              // Skip the first post when showing all posts and not searching (it's already shown in featured section)
              (searchQuery.trim() ? filteredPosts : filteredPosts.slice(1)).map((post) => (
                <div key={post.id} className="relative bg-white rounded-lg shadow-md overflow-hidden flex flex-col h-full hover:shadow-xl transition-shadow">
                  {post.featured_image && (
                    <img
                      src={post.featured_image}
                      alt={post.title}
                      className="w-full h-48 object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  )}
                  <span className="absolute top-2 right-2 flex items-center gap-2 text-xs rounded-full bg-gray-500 px-2 py-1 text-white">
                    <TimerIcon className='w-3 h-3' />
                    {new Date(post.created_at).toLocaleDateString()}
                  </span>
                  <div className="p-4 flex-grow">
                    <div className="flex items-center space-x-2 mb-4">
                      <Tag className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-blue-600 font-medium">News</span>
                    </div>
                    <h3 className="text-xl font-bold mb-2 line-clamp-2">{post.title}</h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {post.excerpt || post.content.substring(0, 150) + '...'}
                    </p>
                  </div>
                  <div className="p-4 border-t">
                    <button
                      onClick={() => navigate(`/news/${post.id}`)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center justify-center transition-colors duration-200"
                    >
                      Read Full Story
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-500 text-lg">
                  {searchQuery ? 'No news articles match your search.' : 'No news articles found.'}
                </p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}