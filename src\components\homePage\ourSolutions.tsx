import React, { useState } from 'react';

// Arrow Icon for buttons
const ArrowRightIcon = ({ color = "currentColor" }: { color?: string }) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    style={{ display: 'block' }}
  >
    <line x1="5" y1="12" x2="19" y2="12"></line>
    <polyline points="12 5 19 12 12 19"></polyline>
  </svg>
);

// Left Icon Component (Blue gradient when active)
const LeftIcon = ({ isActive = false, size = 150 }: { isActive?: boolean; size?: number }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 326 327"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={{
      transition: 'all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1)',
    }}
  >
    <rect y="0.599609" width="326" height="326" rx="163" fill={isActive ? "#EDF0FD" : "#F3F4F6"}/>
    {isActive && (
      <g filter="url(#filter0_i_left)">
        <circle cx="163" cy="163.6" r="123" fill="url(#paint0_radial_left)"/>
      </g>
    )}
    <path d="M120.242 211.571C127.154 211.571 132.757 217.174 132.757 224.085C132.757 230.996 127.154 236.6 120.242 236.6C113.331 236.599 107.729 230.996 107.729 224.085C107.729 217.174 113.331 211.572 120.242 211.571ZM188.028 201.143C194.94 201.143 200.543 206.746 200.543 213.657C200.543 220.568 194.94 226.171 188.028 226.171C181.117 226.171 175.514 220.568 175.514 213.657C175.514 206.746 181.117 201.143 188.028 201.143ZM171.44 219.879L137.533 223.841L136.929 218.662L170.835 214.699L171.44 219.879ZM164.316 176.633L134.018 211.81L130.067 208.407L160.365 173.23L164.316 176.633ZM116.771 206.057L111.726 207.372L101.471 168.044L106.517 166.729L116.771 206.057ZM185.633 197.149L180.646 198.67L174.868 179.722L179.855 178.201L185.633 197.149ZM173.429 151.085C180.34 151.085 185.942 156.688 185.942 163.6C185.942 170.511 180.34 176.114 173.429 176.114C166.517 176.114 160.914 170.511 160.914 163.6C160.914 156.688 166.517 151.085 173.429 151.085ZM225.571 151.085C232.483 151.085 238.086 156.688 238.086 163.6C238.086 170.511 232.483 176.114 225.571 176.114C218.66 176.114 213.057 170.511 213.057 163.6C213.057 156.688 218.66 151.085 225.571 151.085ZM208.886 166.729H191.157V161.514H208.886V166.729ZM100.429 136.485C107.34 136.486 112.942 142.089 112.942 149C112.942 155.911 107.34 161.513 100.429 161.514C93.5174 161.514 87.9143 155.911 87.9141 149C87.9141 142.089 93.5173 136.485 100.429 136.485ZM202.325 130.154L187.039 151.295L182.814 148.24L198.1 127.1L202.325 130.154ZM168.566 148.241L163.876 150.52L148.348 118.541L153.039 116.264L168.566 148.241ZM132.137 116.202L112.442 135.728L108.771 132.024L128.466 112.5L132.137 116.202ZM210.971 102.071C217.882 102.071 223.485 107.674 223.485 114.585C223.485 121.496 217.882 127.1 210.971 127.1C204.059 127.099 198.457 121.496 198.457 114.585C198.457 107.674 204.06 102.072 210.971 102.071ZM144.229 90.5996C151.14 90.5997 156.742 96.2029 156.742 103.114C156.742 110.025 151.14 115.628 144.229 115.628C137.317 115.628 131.714 110.026 131.714 103.114C131.714 96.2028 137.317 90.5996 144.229 90.5996ZM194.787 105.105L193.662 110.197L160.329 102.831L161.454 97.7393L194.787 105.105Z" fill={isActive ? "url(#paint1_linear_left)" : "#9CA3AF"}/>
    <defs>
      {isActive && (
        <>
          <filter id="filter0_i_left" x="40" y="40.5996" width="246" height="246" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="19.9"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_left"/>
          </filter>
          <radialGradient id="paint0_radial_left" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(163 163.6) rotate(90) scale(226)">
            <stop stopColor="#CDD5FF"/>
            <stop offset="1" stopColor="white"/>
          </radialGradient>
          <linearGradient id="paint1_linear_left" x1="163" y1="90.5996" x2="163" y2="236.6" gradientUnits="userSpaceOnUse">
            <stop stopColor="#3B82F6"/>
            <stop offset="1" stopColor="#1E40AF"/>
          </linearGradient>
        </>
      )}
    </defs>
  </svg>
);

// Center Icon Component (Original blue gradient)
const CenterIcon = ({ isActive = false, size = 150 }: { isActive?: boolean; size?: number }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 326 327"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={{
      transition: 'all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1)',
    }}
  >
    <rect y="0.599609" width="326" height="326" rx="163" fill={isActive ? "#EDF0FD" : "#F3F4F6"}/>
    {isActive && (
      <g filter="url(#filter0_i_center)">
        <circle cx="163" cy="163.6" r="123" fill="url(#paint0_radial_center)"/>
      </g>
    )}
    <path d="M120.242 211.571C127.154 211.571 132.757 217.174 132.757 224.085C132.757 230.996 127.154 236.6 120.242 236.6C113.331 236.599 107.729 230.996 107.729 224.085C107.729 217.174 113.331 211.572 120.242 211.571ZM188.028 201.143C194.94 201.143 200.543 206.746 200.543 213.657C200.543 220.568 194.94 226.171 188.028 226.171C181.117 226.171 175.514 220.568 175.514 213.657C175.514 206.746 181.117 201.143 188.028 201.143ZM171.44 219.879L137.533 223.841L136.929 218.662L170.835 214.699L171.44 219.879ZM164.316 176.633L134.018 211.81L130.067 208.407L160.365 173.23L164.316 176.633ZM116.771 206.057L111.726 207.372L101.471 168.044L106.517 166.729L116.771 206.057ZM185.633 197.149L180.646 198.67L174.868 179.722L179.855 178.201L185.633 197.149ZM173.429 151.085C180.34 151.085 185.942 156.688 185.942 163.6C185.942 170.511 180.34 176.114 173.429 176.114C166.517 176.114 160.914 170.511 160.914 163.6C160.914 156.688 166.517 151.085 173.429 151.085ZM225.571 151.085C232.483 151.085 238.086 156.688 238.086 163.6C238.086 170.511 232.483 176.114 225.571 176.114C218.66 176.114 213.057 170.511 213.057 163.6C213.057 156.688 218.66 151.085 225.571 151.085ZM208.886 166.729H191.157V161.514H208.886V166.729ZM100.429 136.485C107.34 136.486 112.942 142.089 112.942 149C112.942 155.911 107.34 161.513 100.429 161.514C93.5174 161.514 87.9143 155.911 87.9141 149C87.9141 142.089 93.5173 136.485 100.429 136.485ZM202.325 130.154L187.039 151.295L182.814 148.24L198.1 127.1L202.325 130.154ZM168.566 148.241L163.876 150.52L148.348 118.541L153.039 116.264L168.566 148.241ZM132.137 116.202L112.442 135.728L108.771 132.024L128.466 112.5L132.137 116.202ZM210.971 102.071C217.882 102.071 223.485 107.674 223.485 114.585C223.485 121.496 217.882 127.1 210.971 127.1C204.059 127.099 198.457 121.496 198.457 114.585C198.457 107.674 204.06 102.072 210.971 102.071ZM144.229 90.5996C151.14 90.5997 156.742 96.2029 156.742 103.114C156.742 110.025 151.14 115.628 144.229 115.628C137.317 115.628 131.714 110.026 131.714 103.114C131.714 96.2028 137.317 90.5996 144.229 90.5996ZM194.787 105.105L193.662 110.197L160.329 102.831L161.454 97.7393L194.787 105.105Z" fill={isActive ? "url(#paint1_linear_center)" : "#9CA3AF"}/>
    <defs>
      {isActive && (
        <>
          <filter id="filter0_i_center" x="40" y="40.5996" width="246" height="246" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="19.9"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_center"/>
          </filter>
          <radialGradient id="paint0_radial_center" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(163 163.6) rotate(90) scale(226)">
            <stop stopColor="#CDD5FF"/>
            <stop offset="1" stopColor="white"/>
          </radialGradient>
          <linearGradient id="paint1_linear_center" x1="163" y1="90.5996" x2="163" y2="236.6" gradientUnits="userSpaceOnUse">
            <stop stopColor="#579AB4"/>
            <stop offset="1" stopColor="#8185D7"/>
          </linearGradient>
        </>
      )}
    </defs>
  </svg>
);

// Right Icon Component (Orange gradient when active)
const RightIcon = ({ isActive = false, size = 150 }: { isActive?: boolean; size?: number }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 326 327"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={{
      transition: 'all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1)',
    }}
  >
    <rect y="0.599609" width="326" height="326" rx="163" fill={isActive ? "#FEF3E2" : "#F3F4F6"}/>
    {isActive && (
      <g filter="url(#filter0_i_right)">
        <circle cx="163" cy="163.6" r="123" fill="url(#paint0_radial_right)"/>
      </g>
    )}
    <path d="M120.242 211.571C127.154 211.571 132.757 217.174 132.757 224.085C132.757 230.996 127.154 236.6 120.242 236.6C113.331 236.599 107.729 230.996 107.729 224.085C107.729 217.174 113.331 211.572 120.242 211.571ZM188.028 201.143C194.94 201.143 200.543 206.746 200.543 213.657C200.543 220.568 194.94 226.171 188.028 226.171C181.117 226.171 175.514 220.568 175.514 213.657C175.514 206.746 181.117 201.143 188.028 201.143ZM171.44 219.879L137.533 223.841L136.929 218.662L170.835 214.699L171.44 219.879ZM164.316 176.633L134.018 211.81L130.067 208.407L160.365 173.23L164.316 176.633ZM116.771 206.057L111.726 207.372L101.471 168.044L106.517 166.729L116.771 206.057ZM185.633 197.149L180.646 198.67L174.868 179.722L179.855 178.201L185.633 197.149ZM173.429 151.085C180.34 151.085 185.942 156.688 185.942 163.6C185.942 170.511 180.34 176.114 173.429 176.114C166.517 176.114 160.914 170.511 160.914 163.6C160.914 156.688 166.517 151.085 173.429 151.085ZM225.571 151.085C232.483 151.085 238.086 156.688 238.086 163.6C238.086 170.511 232.483 176.114 225.571 176.114C218.66 176.114 213.057 170.511 213.057 163.6C213.057 156.688 218.66 151.085 225.571 151.085ZM208.886 166.729H191.157V161.514H208.886V166.729ZM100.429 136.485C107.34 136.486 112.942 142.089 112.942 149C112.942 155.911 107.34 161.513 100.429 161.514C93.5174 161.514 87.9143 155.911 87.9141 149C87.9141 142.089 93.5173 136.485 100.429 136.485ZM202.325 130.154L187.039 151.295L182.814 148.24L198.1 127.1L202.325 130.154ZM168.566 148.241L163.876 150.52L148.348 118.541L153.039 116.264L168.566 148.241ZM132.137 116.202L112.442 135.728L108.771 132.024L128.466 112.5L132.137 116.202ZM210.971 102.071C217.882 102.071 223.485 107.674 223.485 114.585C223.485 121.496 217.882 127.1 210.971 127.1C204.059 127.099 198.457 121.496 198.457 114.585C198.457 107.674 204.06 102.072 210.971 102.071ZM144.229 90.5996C151.14 90.5997 156.742 96.2029 156.742 103.114C156.742 110.025 151.14 115.628 144.229 115.628C137.317 115.628 131.714 110.026 131.714 103.114C131.714 96.2028 137.317 90.5996 144.229 90.5996ZM194.787 105.105L193.662 110.197L160.329 102.831L161.454 97.7393L194.787 105.105Z" fill={isActive ? "url(#paint1_linear_right)" : "#9CA3AF"}/>
    <defs>
      {isActive && (
        <>
          <filter id="filter0_i_right" x="40" y="40.5996" width="246" height="246" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="19.9"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_right"/>
          </filter>
          <radialGradient id="paint0_radial_right" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(163 163.6) rotate(90) scale(226)">
            <stop stopColor="#FED7AA"/>
            <stop offset="1" stopColor="white"/>
          </radialGradient>
          <linearGradient id="paint1_linear_right" x1="163" y1="90.5996" x2="163" y2="236.6" gradientUnits="userSpaceOnUse">
            <stop stopColor="#F97316"/>
            <stop offset="1" stopColor="#EA580C"/>
          </linearGradient>
        </>
      )}
    </defs>
  </svg>
);

const OurSolutions: React.FC = () => {
  const [activeIcon, setActiveIcon] = useState<'left' | 'center' | 'right'>('center');
  const [isMobile, setIsMobile] = useState(false);
  const textBlack = '#000000';
  const textGray = '#6B7280';
  const primaryBlue = '#6366F1';

  // Track screen size for responsive design
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Add CSS keyframes for smooth content transitions
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeInUp {
        0% {
          opacity: 0;
          transform: translateY(20px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }
    `;
    document.head.appendChild(style);
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  // Content for each solution
  const solutionContent = {
    left: {
      title: "Situational Awareness at Your Fingertips",
      description: "A comprehensive emergency response platform providing real-time situational awareness and operational support.",
      highlight: ""
    },
    center: {
      title: "International Responders  Systems  GrantReady",
      description: "Read the white paper and see how GrantReady can supercharge your grants processing",
      highlight: "GrantReady"
    },
    right: {
      title: "Predict Tomorrow, Act Today",
      description: "An all-in-one, cloud-based platform designed by public health practitioners to streamline grant planning, execution, and reporting.",
      highlight: ""
    }
  };

  const currentContent = solutionContent[activeIcon];

  return (
    <div style={{
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
      backgroundColor: '#FFFFFF',
      padding: '80px 20px',
      textAlign: 'center',
    }}>
      {/* Content Wrapper */}
      <div style={{
        maxWidth: '960px',
        margin: '0 auto',
      }}>
        {/* Header */}
        <h2 style={{
          fontSize: 'clamp(2rem, 4vw, 2.5rem)',
          fontWeight: 700,
          color: textBlack,
          margin: '0 0 16px 0',
          lineHeight: 1.2,
        }}>
          Our Solutions
        </h2>

        <p style={{
          fontSize: 'clamp(1rem, 2.5vw, 1.125rem)',
          color: textGray,
          fontWeight: 400,
          maxWidth: '600px',
          margin: '0 auto 60px auto',
          lineHeight: 1.5,
        }}>
          Comprehensive platforms designed for healthcare excellence
        </p>

        {/* Icons Row */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 'clamp(20px, 4vw, 40px)',
          marginBottom: '60px',
          flexWrap: 'nowrap',
          padding: '0 20px',
        }}>
          {/* Left Icon */}
          <div
            style={{
              cursor: 'pointer',
              transition: 'all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1)',
            }}
            onClick={() => setActiveIcon('left')}
          >
            <LeftIcon
              isActive={activeIcon === 'left'}
              size={isMobile ? (activeIcon === 'left' ? 90 : 50) : (activeIcon === 'left' ? 200 : 150)}
            />
          </div>

          {/* Center Icon */}
          <div
            style={{
              cursor: 'pointer',
              transition: 'all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1)',
            }}
            onClick={() => setActiveIcon('center')}
          >
            <CenterIcon
              isActive={activeIcon === 'center'}
              size={isMobile ? (activeIcon === 'center' ? 90 : 50) : (activeIcon === 'center' ? 200 : 150)}
            />
          </div>

          {/* Right Icon */}
          <div
            style={{
              cursor: 'pointer',
              transition: 'all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1)',
            }}
            onClick={() => setActiveIcon('right')}
          >
            <RightIcon
              isActive={activeIcon === 'right'}
              size={isMobile ? (activeIcon === 'right' ? 90 : 50) : (activeIcon === 'right' ? 200 : 150)}
            />
          </div>
        </div>

        {/* Main Content */}
        <div
          key={activeIcon}
          style={{
            position: 'relative',
            display: 'inline-block',
            transition: 'all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1)',
            opacity: 1,
            transform: 'translateY(0)',
            animation: 'fadeInUp 0.8s cubic-bezier(0.25, 0.1, 0.25, 1)',
          }}
        >
          <h3 style={{
            fontSize: 'clamp(1.5rem, 3vw, 1.875rem)',
            fontWeight: 700,
            color: textBlack,
            margin: '0 0 16px 0',
            lineHeight: 1.3,
            transition: 'all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1)',
            opacity: 1,
            transform: 'translateY(0)',
          }}>
            {currentContent.highlight ? (
              <>
                {currentContent.title.replace(currentContent.highlight, '')}
                <span style={{ color: primaryBlue }}>
                  {currentContent.highlight}
                </span>
                <sup style={{
                  color: primaryBlue,
                  fontSize: '0.6em',
                  position: 'relative',
                  display: 'inline-block'
                }}>
                  TM
                  <svg
                    width="100"
                    height="100"
                    viewBox="0 0 166 165"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{
                      position: 'absolute',
                      top: '-70px',
                      left: '50%',
                      transform: 'translateX(-30%) rotate(15deg)',
                      transformOrigin: 'center'
                    }}
                  >
                  <g clipPath="url(#clip0_141_1253)">
                    <path d="M62.8641 52.2697C62.8108 53.6426 62.7514 55.0148 62.6554 56.3872C62.613 57.1151 62.5619 57.8459 62.569 58.5743C62.6109 62.4623 62.6996 66.3526 62.6464 70.2412C62.6023 72.6349 62.5332 75.028 62.439 77.4206L62.2867 81.0086L62.094 84.5943C62.036 85.3188 61.9195 86.037 61.7456 86.742C61.6874 87.034 61.56 87.3074 61.3737 87.5398C61.1875 87.7722 60.9478 87.9568 60.6744 88.0784C59.9247 88.4319 59.1669 87.9829 59.0277 87.0075C58.9528 86.3711 58.9584 85.7286 59.0445 85.0949C59.2124 83.6476 59.3174 82.1984 59.3595 80.7473C59.4069 79.2963 59.3944 77.8432 59.322 76.3878C59.2828 75.5844 59.1944 74.7467 59.3257 73.973C59.7423 71.5009 59.6165 68.9888 59.8268 66.5023C59.9818 64.654 59.996 62.7907 60.095 60.9358L60.3552 55.9735C60.4324 54.3191 60.5513 52.6652 60.6337 51.0101C60.6842 50.3664 60.7774 49.7271 60.9127 49.0963C61.0556 48.5031 61.2317 47.9185 61.4401 47.3452C61.6232 46.8376 61.981 46.8753 62.1555 47.47C62.4041 48.3236 62.5923 49.1929 62.719 50.0715C62.7714 50.4323 62.803 50.7975 62.8192 51.1638C62.8203 51.5299 62.8213 51.8972 62.8223 52.2624L62.8641 52.2697Z" fill="#F77D4B"/>
                    <path d="M101.252 64.2246L100.386 65.7511L99.4807 67.2584C99.162 67.7923 98.8414 68.3286 98.5702 68.8872C98.2065 69.6315 97.8521 70.3803 97.5008 71.1305L96.4342 73.3738L94.3117 77.8633L91.6845 83.3845L89.0876 88.9208C88.8089 89.4742 88.4904 90.0068 88.1344 90.5144C87.9972 90.7285 87.8111 90.9073 87.591 91.0367C87.371 91.1661 87.123 91.2424 86.8668 91.2596C86.1613 91.322 85.696 90.7426 85.8799 89.9149C86.0111 89.3796 86.2114 88.8639 86.476 88.3806C87.633 86.1618 88.6468 83.8716 89.5113 81.5235C89.7112 80.8575 89.9871 80.2169 90.3337 79.6141C91.517 77.8065 92.3015 75.7916 93.3683 73.9256C94.1617 72.5404 94.856 71.0958 95.6438 69.7049C97.0582 67.2298 98.4961 64.7706 100.025 62.3646C100.595 61.4449 101.316 60.6265 102.158 59.9424C102.521 59.6455 102.824 59.8491 102.705 60.3619C102.528 61.095 102.303 61.8154 102.031 62.5185C101.795 63.0953 101.525 63.6574 101.222 64.2019L101.252 64.2246Z" fill="#F77D4B"/>
                    <path d="M124.999 93.5581C124.089 94.0167 123.177 94.4715 122.261 94.9224C121.765 95.141 121.283 95.3887 120.816 95.6643C119.591 96.4247 118.383 97.2124 117.176 97.9998C115.968 98.7855 114.756 99.5628 113.532 100.32C112.026 101.256 110.512 102.176 108.999 103.099L104.445 105.843C103.968 106.106 103.46 106.309 102.932 106.449C102.43 106.603 101.901 106.384 101.401 105.98C100.671 105.391 100.414 104.436 100.908 103.865C101.234 103.511 101.632 103.229 102.076 103.036C104.049 102.096 105.902 100.929 107.596 99.5569C108.068 99.1781 108.533 98.7417 109.102 98.5529C110.912 97.9435 112.41 96.7964 114.114 96.0077C115.377 95.4178 116.579 94.7115 117.83 94.0978C118.945 93.5505 120.064 93.0119 121.187 92.4822L124.593 90.9607C125.479 90.559 126.451 90.3738 127.428 90.4204C127.527 90.4193 127.624 90.4474 127.707 90.5013C127.79 90.5551 127.855 90.6323 127.894 90.7227C127.933 90.8132 127.944 90.9129 127.926 91.0091C127.907 91.1053 127.86 91.1935 127.79 91.2625C127.341 91.7537 126.863 92.2178 126.358 92.6523C125.921 92.9734 125.461 93.2619 124.981 93.5154L124.999 93.5581Z" fill="#F77D4B"/>
                    <path d="M120.221 123.539C119.946 123.547 119.672 123.565 119.396 123.556L118.568 123.527C118.275 123.509 117.982 123.516 117.691 123.547C116.919 123.657 116.148 123.797 115.375 123.893C114.603 123.999 113.83 124.099 113.055 124.172C112.101 124.263 111.145 124.337 110.188 124.379L107.317 124.508C107.015 124.5 106.717 124.436 106.437 124.32C106.158 124.223 105.958 123.849 105.818 123.353C105.614 122.632 105.728 121.843 106.104 121.639C106.339 121.523 106.606 121.484 106.866 121.527C108.038 121.667 109.223 121.575 110.355 121.257C110.671 121.171 110.998 121.031 111.317 121.123C112.335 121.405 113.328 121.16 114.336 121.247C115.086 121.317 115.83 121.233 116.58 121.281C117.916 121.361 119.251 121.41 120.59 121.534C121.144 121.577 121.673 121.79 122.103 122.144C122.155 122.188 122.197 122.243 122.226 122.305C122.254 122.366 122.269 122.433 122.268 122.501C122.268 122.569 122.253 122.635 122.224 122.696C122.194 122.757 122.152 122.811 122.1 122.854C121.938 122.952 121.771 123.045 121.602 123.131C121.438 123.219 121.267 123.293 121.09 123.352C120.806 123.428 120.515 123.478 120.221 123.502L120.221 123.539Z" fill="#F77D4B"/>
                    <path d="M30.352 78.338C30.7493 78.8618 31.1405 79.3886 31.5255 79.9186C31.7224 80.2042 31.9364 80.4779 32.1663 80.7382C32.8073 81.4044 33.4421 82.0787 34.0649 82.7657C34.6847 83.455 35.2776 84.1671 35.8437 84.9019C36.5303 85.8128 37.1901 86.7446 37.8093 87.7025C38.4288 88.6601 39.0218 89.6337 39.5883 90.6234C39.7495 90.9377 39.8562 91.2763 39.9041 91.6249C39.968 91.9596 39.7446 92.3175 39.3802 92.657C38.8489 93.1509 38.0913 93.3401 37.7195 93.0407C37.4955 92.8439 37.326 92.5934 37.2276 92.3136C36.7337 91.0699 35.9962 89.9338 35.058 88.9714C34.8008 88.7012 34.4924 88.45 34.3719 88.1031C33.9892 86.9888 33.1497 86.1722 32.5568 85.1927C32.3338 84.8294 32.0912 84.4783 31.8302 84.1407C31.5765 83.7977 31.3122 83.4624 31.0622 83.1161C30.1693 81.8821 29.2613 80.6502 28.2954 79.4588C27.9052 78.9451 27.6904 78.3221 27.6828 77.6817C27.6548 77.3756 27.8919 77.1608 28.2769 77.1751L28.6528 77.2286C28.7116 77.234 28.7689 77.2509 28.8214 77.2781L28.9725 77.3463C29.1712 77.4379 29.3643 77.5412 29.5506 77.6558C29.6994 77.7492 29.8379 77.8579 29.9638 77.9801C30.0833 78.1071 30.1977 78.2395 30.3191 78.3642L30.352 78.338Z" fill="#F77D4B"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_141_1253">
                      <rect width="126.14" height="124.602" fill="white" transform="translate(49.0935) rotate(23.2037)"/>
                    </clipPath>
                  </defs>
                </svg>
                </sup>
              </>
            ) : activeIcon === 'left' ? (
              <>
                Situational Awareness at <span style={{ position: 'relative', display: 'inline-block' }}>
                  Your Fingertips
                  <svg
                    width="350"
                    height="21"
                    viewBox="0 0 350 21"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{
                      position: 'absolute',
                      bottom: '-8px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: '100%',
                      height: 'auto',
                      maxWidth: '280px',
                      opacity: activeIcon === 'left' ? 1 : 0,
                      transition: 'opacity 0.6s ease',
                    }}
                  >
                    <path d="M57.5417 7.41597C148.43 3.66792 228.951 1.20972 287.293 0.401912C325.4 -0.125474 345.893 -0.133835 348.087 0.376823C350.353 0.904209 350.685 2.66377 348.656 3.3963C347.846 3.68861 335.937 4.25958 320.064 4.7676C270.108 6.36604 205.455 9.35515 152.681 12.5067C114.749 14.7716 114.325 14.8341 139.017 14.5154C151.264 14.3578 190.336 14.2284 225.844 14.2284C282.129 14.2284 290.527 14.3129 291.364 14.8887C291.891 15.2519 292.329 15.6908 292.339 15.8638C292.394 16.9939 285.144 17.159 218.677 17.5428C145.628 17.965 126.105 18.2908 86.3871 19.7519C46.5136 21.2191 46.9519 21.2094 44.5714 20.6552C41.8468 20.0208 41.6757 18.3757 44.2313 17.3703C47.9549 15.9048 94.7513 12.1523 149.139 8.95895C190.267 6.54389 190.536 6.52364 177.984 6.77853C162.326 7.09681 111.518 8.9365 62.8068 10.9496C36.8702 12.0216 16.1258 12.6696 10.1765 12.5934C0.940905 12.475 0.337642 12.4098 0.0410905 11.5012C-0.478127 9.91599 3.13212 9.65978 57.5417 7.41597Z" fill="#319DB7"/>
                  </svg>
                </span>
              </>
            ) : activeIcon === 'right' ? (
              <>
                Predict Tomorrow, <span style={{ position: 'relative', display: 'inline-block' }}>
                  Act Today
                  <svg
                    width="350"
                    height="21"
                    viewBox="0 0 350 21"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{
                      position: 'absolute',
                      bottom: '-8px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: '100%',
                      height: 'auto',
                      maxWidth: '250px',
                      opacity: activeIcon === 'right' ? 1 : 0,
                      transition: 'opacity 0.6s ease',
                    }}
                  >
                    <path d="M57.5417 7.41597C148.43 3.66792 228.951 1.20972 287.293 0.401912C325.4 -0.125474 345.893 -0.133835 348.087 0.376823C350.353 0.904209 350.685 2.66377 348.656 3.3963C347.846 3.68861 335.937 4.25958 320.064 4.7676C270.108 6.36604 205.455 9.35515 152.681 12.5067C114.749 14.7716 114.325 14.8341 139.017 14.5154C151.264 14.3578 190.336 14.2284 225.844 14.2284C282.129 14.2284 290.527 14.3129 291.364 14.8887C291.891 15.2519 292.329 15.6908 292.339 15.8638C292.394 16.9939 285.144 17.159 218.677 17.5428C145.628 17.965 126.105 18.2908 86.3871 19.7519C46.5136 21.2191 46.9519 21.2094 44.5714 20.6552C41.8468 20.0208 41.6757 18.3757 44.2313 17.3703C47.9549 15.9048 94.7513 12.1523 149.139 8.95895C190.267 6.54389 190.536 6.52364 177.984 6.77853C162.326 7.09681 111.518 8.9365 62.8068 10.9496C36.8702 12.0216 16.1258 12.6696 10.1765 12.5934C0.940905 12.475 0.337642 12.4098 0.0410905 11.5012C-0.478127 9.91599 3.13212 9.65978 57.5417 7.41597Z" fill="#F59E0B"/>
                  </svg>
                </span>
              </>
            ) : (
              currentContent.title
            )}
          </h3>
        </div>

        <p style={{
          fontSize: 'clamp(0.95rem, 2vw, 1.125rem)',
          color: textGray,
          fontWeight: 400,
          maxWidth: '500px',
          margin: '0 auto 40px auto',
          lineHeight: 1.6,
          transition: 'all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1)',
          opacity: 1,
          transform: 'translateY(0)',
        }}>
          {currentContent.description}
        </p>

        {/* Buttons */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '20px',
          flexWrap: 'wrap',
        }}>
          <button style={{
            backgroundColor: textBlack,
            color: '#FFFFFF',
            border: `2px solid ${textBlack}`,
            padding: '8px 6px 8px 8px',
            borderRadius: '8px',
            fontSize: 'clamp(0.9rem, 2vw, 1rem)',
            fontWeight: 600,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            transition: 'background-color 0.2s ease, border-color 0.2s ease',
            letterSpacing: '0.5px',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = '#333333';
            e.currentTarget.style.borderColor = '#333333';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = textBlack;
            e.currentTarget.style.borderColor = textBlack;
          }}
          >
            Read Whitepaper
            <span style={{
                backgroundColor: '#FFFFFF',
                padding: '6px',
                borderRadius: '6px',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '12px',
                lineHeight: 0,
            }}>
              <ArrowRightIcon color={textBlack} />
            </span>
          </button>

          <button style={{
            backgroundColor: '#FFFFFF',
            color: textBlack,
            border: `2px solid #9CA3AF`,
            padding: '5px 6px 5px 8px',
            // padding: '12px 16px 12px 24px',
            borderRadius: '8px',
            fontSize: 'clamp(0.9rem, 2vw, 1rem)',
            fontWeight: 600,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            transition: 'background-color 0.2s ease, color 0.2s ease',
            letterSpacing: '0.5px',
          }}
          onMouseOver={(e) => { e.currentTarget.style.backgroundColor = '#F0F0F0'; }}
          onMouseOut={(e) => { e.currentTarget.style.backgroundColor = '#FFFFFF'; }}
          >
            See A Demo
            <span style={{
                padding: '6px',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '12px',
                lineHeight: 0,
            }}>
              <ArrowRightIcon color={textBlack} />
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default OurSolutions;
