import React from 'react';
import { FileText, Shield, Users, Clock, <PERSON><PERSON>hart as <PERSON><PERSON>ar, Lock, Briefcase, Award, Brain, Building2, Zap, Phone, BookOpen, Download } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function Solutions() {
  const services = [
    "Virtual and Online Training and webinars",
    "Studio production, design, engineering and installation",
    "Healthcare Disaster Response Planning and Training",
    "Public Health Emergency Training and Exercises",
    "Operational Support and training in HAZMAT/Health Physics/Chemistry/Highly Infectious Disease Response and Law Enforcement",
    "Response and Safety Plan Development",
    "ICS/EOC Position Specific Training",
    "NIMS and ICS Training",
    "HSEEP Compliant Exercises",
    "Just in Time Response Training",
    "Training in Spanish",
    "Training in Hazard, Vulnerability and Health Risk Assessment",
    "Emergency Operations Facility Support, construction & Management",
    "NIMS Compliance",
    "Highly Infectious Disease Response Planning and Exercises",
    "Project and Program Management",
    "Role Player Training and Support"
  ];

  const naicsCodes = [
    { code: "541690", description: "Other Scientific and Technical Consulting Services" },
    { code: "541715", description: "Research and Development in the Physical, Engineering, and Life Sciences" },
    { code: "561110", description: "Office Administrative Services" },
    { code: "611420", description: "Computer Training" },
    { code: "611430", description: "Professional and Management Development Training" },
    { code: "611699", description: "All Other Miscellaneous Schools and Instruction" },
    { code: "611710", description: "Educational Support Services" }
  ];

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center flex flex-col items-center justify-center">
          <h1 className="text-4xl font-bold mb-6 max-w-3xl">World-class Solutions</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
            Our team has domain knowledge in government, healthcare, disaster and emergency response, and engineering. We use our expertise to deliver sound solutions to our clients spanning multiple fields and disciplines to fit their needs.
          </p>
        </div>
      </section>

      {/* Primary Services Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Primary Services</h2>
            <p className="text-xl text-gray-600">
              We offer a comprehensive list of premium services tailored to our customers' unique requirements
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-start">
                  <Award className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
                  <p className="ml-4 text-gray-700">{service}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Platforms Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Our Platforms</h2>
            <p className="text-xl text-gray-600">
              Our custom platforms transform laborious and sluggish workflows into streamlined and manageable systems
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                alt="GrantReady Platform"
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">GrantReady™</h3>
                <p className="text-gray-600 mb-4">Streamline your grant management process with our comprehensive solution.</p>
                <Link to="/solutions/grantready" className="text-blue-600 hover:text-blue-700 font-medium">Learn more →</Link>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1563986768494-4dee2763ff3f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                alt="SOAR Platform"
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">SOAR</h3>
                <p className="text-gray-600 mb-4">STLT Outbreak Analytics & Response - Disease outbreak prediction and response.</p>
                <Link to="/solutions/soar" className="text-blue-600 hover:text-blue-700 font-medium">Learn more →</Link>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                alt="ELENOR Platform"
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">ELENOR</h3>
                <p className="text-gray-600 mb-4">Advanced emergency response and coordination platform.</p>
                <Link to="/solutions/elenor" className="text-blue-600 hover:text-blue-700 font-medium">Learn more →</Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Capabilities Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Capabilities</h2>
            <p className="text-xl text-gray-600">
              We support a diverse range of healthcare and disaster response needs for municipal, governmental, institutional, commercial, and industrial clients.
            </p>
          </div>

          {/* NAICS Codes */}
          <div className="bg-gray-50 rounded-lg p-8">
            <h3 className="text-2xl font-bold mb-6">NAICS Codes</h3>
            <div className="grid md:grid-cols-2 gap-6">
              {naicsCodes.map((item, index) => (
                <div key={index} className="flex items-start">
                  <Building2 className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
                  <div className="ml-4">
                    <p className="font-semibold">{item.code}</p>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Resources Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Resources</h2>
            <p className="text-xl text-gray-600">
              Explore our collection of whitepapers, guides, and other resources to learn more about our solutions and industry best practices.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-blue-100 mr-4">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold">Whitepapers</h3>
                </div>
                <p className="text-gray-600 mb-6">
                  In-depth reports and analyses on healthcare, emergency management, and grant administration topics.
                </p>
                <Link to="/whitepapers" className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
                  Browse Whitepapers
                  <Download className="h-4 w-4 ml-2" />
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-green-100 mr-4">
                    <BookOpen className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="text-xl font-bold">Guides</h3>
                </div>
                <p className="text-gray-600 mb-6">
                  Step-by-step guides and best practices for implementing effective healthcare and emergency response systems.
                </p>
                <Link to="/guides" className="inline-flex items-center text-green-600 hover:text-green-700 font-medium">
                  View Guides
                  <Download className="h-4 w-4 ml-2" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Operations?</h2>
          <p className="text-xl mb-8">Get in touch with our team to learn how we can help.</p>
          <Link to="/contact" className="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300">
            <Phone className="h-5 w-5 mr-2" />
            Contact Us
          </Link>
        </div>
      </section>
    </div>
  );
}
