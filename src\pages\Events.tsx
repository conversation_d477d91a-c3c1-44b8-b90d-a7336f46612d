import { useState, useEffect } from 'react';
import { useScrollPosition } from '../hooks/useScrollPosition';
import { Calendar, MapPin, Users, Clock, Search, AlertCircle, CheckCircle, MonitorPlay } from 'lucide-react';
import EventRegistrationModal from '../components/EventRegistrationModal';
import EventSuccessModal from '../components/EventSuccessModal';
import CancellationConfirmModal from '../components/CancellationConfirmModal';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';

// Helper function to convert 24-hour time to 12-hour time with AM/PM
const formatTimeWithAMPM = (time: string): string => {
  if (!time) return '';

  // Parse the time string (expected format: HH:MM)
  const [hours, minutes] = time.split(':').map(Number);

  if (isNaN(hours) || isNaN(minutes)) return time;

  // Convert to 12-hour format
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
};

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  end_time?: string;
  location: string;
  address?: string;
  type: string;
  capacity?: number;
  image_url: string;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
  attendees?: number; // For UI display
}

// EventRegistration interface is used in the backend
// but not directly in this component

export default function Events() {
  const user = useAuthStore((state) => state.user);
  const [events, setEvents] = useState<Event[]>([]);
  const [featuredEvent, setFeaturedEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredEvents, setFilteredEvents] = useState<Event[]>([]);
  const [userRegistrations, setUserRegistrations] = useState<{[key: string]: boolean}>({});
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [expandedDescriptions, setExpandedDescriptions] = useState<Record<string, boolean>>({});
  const { saveScrollPosition, restoreScrollPosition } = useScrollPosition();

  useEffect(() => {
    fetchEvents();
  }, []);

  useEffect(() => {
    const fetchUserRegistrations = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('event_registrations')
          .select('event_id, status')
          .eq('user_id', user.id)
          .neq('status', 'cancelled');

        if (error) throw error;

        const registrations: {[key: string]: boolean} = {};
        if (data) {
          data.forEach(reg => {
            registrations[reg.event_id] = true;
          });
        }

        setUserRegistrations(registrations);
      } catch (err) {
        console.error('Error fetching user registrations:', err);
      }
    };

    if (user) {
      fetchUserRegistrations();
    } else {
      setUserRegistrations({});
    }
  }, [user]);

  // Filter events based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      // When no search, show all events in the grid
      setFilteredEvents(events);
      return;
    }

    const query = searchQuery.toLowerCase();

    // When searching, include ALL events (including featured) in the results
    const allEvents = events;

    const filtered = allEvents.filter(event =>
      event.title.toLowerCase().includes(query) ||
      event.description.toLowerCase().includes(query) ||
      event.location.toLowerCase().includes(query) ||
      event.type.toLowerCase().includes(query)
    );

    setFilteredEvents(filtered);
  }, [searchQuery, events]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all events
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .order('date', { ascending: true });

      if (error) throw error;

      if (data) {
        // Find featured event
        const featured = data.find(event => event.is_featured);
        if (featured) {
          setFeaturedEvent(featured);
          // Keep all events in the events list, including featured
          setEvents(data);
          setFilteredEvents(data);
        } else {
          setEvents(data);
          setFilteredEvents(data);
          // If no featured event, use the first one
          if (data.length > 0) {
            setFeaturedEvent(data[0]);
          }
        }
      }
    } catch (err: unknown) {
      console.error('Error fetching events:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // This function is now defined inside the useEffect

  // Function to toggle description expansion
  const toggleDescription = (eventId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [eventId]: !prev[eventId]
    }));
  };

  // Function to truncate text
  const truncateText = (text: string, wordCount: number = 15) => {
    if (!text) return '';

    // Handle case where there might be very long words
    const words = text.split(' ');

    // If text is already short enough, return it as is
    if (words.length <= wordCount && text.length < 100) return text;

    // Otherwise truncate
    return words.slice(0, wordCount).join(' ') + '...';
  };

  // Function to check if text needs truncation
  const needsTruncation = (text: string): boolean => {
    if (!text) return false;
    const words = text.split(' ');
    return words.length > 15 || text.length > 100;
  };

  const handleRegisterClick = async (event: Event) => {
    // Save current scroll position before showing any modal
    saveScrollPosition();

    if (!user) {
      // Show registration modal for non-logged in users
      setSelectedEvent(event);
      setShowRegistrationModal(true);
      return;
    }

    // If already registered, show confirmation to cancel
    if (userRegistrations[event.id]) {
      setSelectedEvent(event);
      setShowCancellationModal(true);
      return;
    }

    try {
      setLoading(true);

      // Check if user previously had a cancelled registration
      const { data: existingRegistrations, error: checkError } = await supabase
        .from('event_registrations')
        .select('id, status')
        .eq('event_id', event.id)
        .eq('user_id', user.id);

      if (checkError) throw checkError;

      // If there's a cancelled registration, reactivate it
      const cancelledRegistration = existingRegistrations?.find(reg => reg.status === 'cancelled');

      if (cancelledRegistration) {
        // Update the existing registration instead of creating a new one
        const { error: updateError } = await supabase
          .from('event_registrations')
          .update({ status: 'registered' })
          .eq('id', cancelledRegistration.id);

        if (updateError) throw updateError;

        // Send confirmation email
        try {
          // Get event details to include in the email
          const { data: eventData, error: eventError } = await supabase
            .from('events')
            .select('title, date, time, location, address')
            .eq('id', event.id)
            .single();

          if (eventError) throw eventError;

          // Send email using the send-contact-email edge function
          const { error: emailError } = await supabase.functions.invoke('send-contact-email', {
            body: {
              to: user.email,
              subject: `Event Registration Confirmation: ${event.title}`,
              html: `
                <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                  <h2 style="color: #2c3e50;">Registration Confirmation</h2>
                  <p>Hello ${user.user_metadata?.full_name || 'there'},</p>
                  <p>Thank you for registering for <strong>${event.title}</strong>. Your registration has been confirmed.</p>

                  <div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #3498db;">Event Details</h3>
                    <p><strong>Date:</strong> ${eventData.date}</p>
                    <p><strong>Time:</strong> ${eventData.time}</p>
                    <p><strong>Location:</strong> ${eventData.location}</p>
                    ${eventData.address ? `<p><strong>Address:</strong> ${eventData.address}</p>` : ''}
                  </div>

                  <p>We look forward to seeing you at the event. If you have any questions, please don't hesitate to contact us.</p>
                  <p>Best regards,<br>International Responder Systems</p>
                </div>
              `
            }
          });

          if (emailError) {
            console.error('Error sending confirmation email:', emailError);
            // Continue with success even if email fails
          }
        } catch (emailErr) {
          console.error('Error in email process:', emailErr);
          // Continue with success even if email fails
        }

        // Update local state
        setUserRegistrations(prev => ({
          ...prev,
          [event.id]: true
        }));

        // Restore scroll position
        restoreScrollPosition();

        // Show success message
        setSuccessMessage('Successfully registered for the event');
        setTimeout(() => setSuccessMessage(null), 3000);

        // Refresh events to update counts
        fetchEvents();
        return;
      }

      // If no cancelled registration exists, show the registration modal
      setSelectedEvent(event);
      setShowRegistrationModal(true);
    } catch (err) {
      console.error('Error checking registration status:', err);
      setError('Failed to process registration. Please try again.');
      setTimeout(() => setError(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  const handleRegistrationSuccess = () => {
    setShowRegistrationModal(false);

    if (selectedEvent) {
      // Update local state first
      if (user) {
        setUserRegistrations(prev => ({
          ...prev,
          [selectedEvent.id]: true
        }));
      }

      // Restore scroll position immediately
      restoreScrollPosition();

      // Then show success modal and refresh data
      setTimeout(() => {
        setShowSuccessModal(true);
        // Refresh the event data to update registration counts
        fetchEvents();
      }, 50);
    }
  };

  const handleCancelRegistration = async (eventId: string) => {
    if (!user) return;

    try {
      setLoading(true);

      // Update the registration status to 'cancelled'
      const { error } = await supabase
        .from('event_registrations')
        .update({ status: 'cancelled' })
        .eq('event_id', eventId)
        .eq('user_id', user.id);

      if (error) throw error;

      // Update local state
      setUserRegistrations(prev => {
        const updated = {...prev};
        delete updated[eventId];
        return updated;
      });

      // Show success message
      setSuccessMessage('Registration successfully cancelled');
      setTimeout(() => setSuccessMessage(null), 3000);

      // Restore scroll position after all state updates
      setTimeout(() => {
        restoreScrollPosition();
      }, 100);
    } catch (err) {
      console.error('Error cancelling registration:', err);
      setError('Failed to cancel registration. Please try again.');
      setTimeout(() => setError(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-left">
            <h1 className="text-4xl font-bold mb-6">Upcoming Events</h1>
            <p className="text-xl mb-10 text-blue-100">
              Join us at our industry-leading events and connect with healthcare emergency response experts
            </p>
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Error Message */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Success Message */}
      {successMessage && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg flex items-start">
            <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{successMessage}</span>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="py-32 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <>
          {/* Featured Event - Only show when not searching */}
          {featuredEvent && searchQuery.trim() === '' && (
            <section className="py-16">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="bg-white rounded-lg shadow-xl overflow-hidden">
                  <div className="md:flex">
                    <div className="md:w-1/2">
                      {featuredEvent.image_url ? (
                        <img
                          src={featuredEvent.image_url}
                          alt={featuredEvent.title}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.style.display = 'none';
                            target.parentElement!.innerHTML = `
                              <div class="h-full w-full bg-blue-100 flex items-center justify-center">
                                <div class="text-center p-8">
                                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mx-auto mb-4">
                                    <rect x="2" y="6" width="20" height="12" rx="2" ry="2"></rect>
                                    <path d="M12 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                    <path d="M17 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                    <path d="M7 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                  </svg>
                                  <p class="text-blue-600 font-medium text-lg px-2">${featuredEvent.title}</p>
                                </div>
                              </div>
                            `;
                          }}
                        />
                      ) : (
                        <div className="h-full w-full bg-blue-100 flex items-center justify-center">
                          <div className="text-center p-8">
                            <MonitorPlay className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                            <p className="text-blue-600 font-medium text-lg px-2">{featuredEvent.title}</p>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="md:w-1/2 p-8">
                      <span className="inline-block px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mb-4">
                        Featured Event
                      </span>
                      <h2 className="text-2xl font-bold mb-4 line-clamp-2">{featuredEvent.title}</h2>
                      <div className="text-gray-600 mb-4 min-h-[80px] overflow-hidden">
                        {expandedDescriptions[featuredEvent.id] ? (
                          <>
                            <p className="break-words break-all">{featuredEvent.description}</p>
                            <button
                              onClick={() => toggleDescription(featuredEvent.id)}
                              className="text-blue-600 text-sm mt-1 hover:underline"
                            >
                              See Less
                            </button>
                          </>
                        ) : (
                          <>
                            <p className="break-words break-all">{truncateText(featuredEvent.description)}</p>
                            {featuredEvent.description && needsTruncation(featuredEvent.description) && (
                              <button
                                onClick={() => toggleDescription(featuredEvent.id)}
                                className="text-blue-600 text-sm mt-1 hover:underline"
                              >
                                See More
                              </button>
                            )}
                          </>
                        )}
                      </div>
                      <div className="space-y-4 mb-6 mt-auto">
                        <div className="flex items-center">
                          <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                          <span className="text-gray-600">{featuredEvent.date}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-5 w-5 text-gray-400 mr-2" />
                          <span className="text-gray-600">{formatTimeWithAMPM(featuredEvent.time)}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-5 w-5 text-gray-400 mr-2" />
                          <span className="text-gray-600">{featuredEvent.location}</span>
                        </div>
                        {featuredEvent.capacity && (
                          <div className="flex items-center">
                            <Users className="h-5 w-5 text-gray-400 mr-2" />
                            <span className="text-gray-600">{featuredEvent.capacity} Capacity</span>
                          </div>
                        )}
                      </div>
                      <div className="mt-auto">
                        <button
                        onClick={() => handleRegisterClick(featuredEvent)}
                        className={`w-full h-10 px-6 py-2 rounded-lg font-semibold text-sm transition duration-300 ${userRegistrations[featuredEvent.id]
                          ? 'bg-green-600 text-white hover:bg-green-700'
                          : 'bg-blue-600 text-white hover:bg-blue-700'}`}
                      >
                        {userRegistrations[featuredEvent.id] ? 'Registered (Click again to cancel)' : 'Register Now'}
                      </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          )}

          {/* Events Grid */}
          <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="mb-8">
                <h2 className="text-2xl font-bold">
                  {searchQuery.trim() !== '' ? `Search Results for "${searchQuery}"` : 'Upcoming Events'}
                </h2>
              </div>

              {filteredEvents.length === 0 ? (
                <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                  <p className="text-gray-600">
                    {searchQuery.trim() !== ''
                      ? `No events found matching "${searchQuery}". Try a different search term.`
                      : 'No events found. Check back soon for upcoming events!'}
                  </p>
                </div>
              ) : (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {filteredEvents.map((event) => (
                    <div key={event.id} className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-gray-100">
                      <div className="relative">
                        {event.image_url ? (
                          <img
                            src={event.image_url}
                            alt={event.title}
                            className="w-full h-52 object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.onerror = null;
                              target.style.display = 'none';
                              target.parentElement!.innerHTML = `
                                <div class="w-full h-52 bg-blue-100 flex items-center justify-center">
                                  <div class="text-center">
                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mx-auto mb-2">
                                      <rect x="2" y="6" width="20" height="12" rx="2" ry="2"></rect>
                                      <path d="M12 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                      <path d="M17 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                      <path d="M7 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                                    </svg>
                                    <p class="text-blue-600 font-medium text-sm px-2">${event.title}</p>
                                  </div>
                                </div>
                              `;
                            }}
                          />
                        ) : (
                          <div className="w-full h-52 bg-blue-100 flex items-center justify-center">
                            <div className="text-center">
                              <MonitorPlay className="h-12 w-12 text-blue-600 mx-auto mb-2" />
                              <p className="text-blue-600 font-medium text-sm px-2">{event.title}</p>
                            </div>
                          </div>
                        )}
                        <div className="absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-semibold text-white bg-blue-500 shadow-md">
                          {event.type}
                        </div>
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent h-24"></div>
                      </div>
                      <div className="p-6 relative -mt-10">
                        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 flex flex-col h-full">
                          <h3 className="text-lg font-bold mb-2 text-gray-800 line-clamp-2 min-h-[3.5rem]">{event.title}</h3>
                          <div className="text-gray-600 mb-4 text-sm min-h-[80px] overflow-hidden">
                            {expandedDescriptions[event.id] ? (
                              <>
                                <p className="break-words break-all">{event.description}</p>
                                <button
                                  onClick={() => toggleDescription(event.id)}
                                  className="text-blue-600 text-sm mt-1 hover:underline"
                                >
                                  See Less
                                </button>
                              </>
                            ) : (
                              <>
                                <p className="break-words break-all">{truncateText(event.description)}</p>
                                {event.description && needsTruncation(event.description) && (
                                  <button
                                    onClick={() => toggleDescription(event.id)}
                                    className="text-blue-600 text-sm mt-1 hover:underline"
                                  >
                                    See More
                                  </button>
                                )}
                              </>
                            )}
                          </div>

                          <div className="grid grid-cols-2 gap-3 mb-4 mt-auto">
                            <div className="flex items-center text-gray-600 text-sm">
                              <Calendar className="h-4 w-4 mr-2 text-blue-500" />
                              <span>{event.date}</span>
                            </div>
                            <div className="flex items-center text-gray-600 text-sm">
                              <Clock className="h-4 w-4 mr-2 text-blue-500" />
                              <span>{formatTimeWithAMPM(event.time)}</span>
                            </div>
                            <div className="flex items-center text-gray-600 text-sm">
                              <MapPin className="h-4 w-4 mr-2 text-blue-500" />
                              <span>{event.location}</span>
                            </div>
                            {event.capacity && (
                              <div className="flex items-center text-gray-600 text-sm">
                                <Users className="h-4 w-4 mr-2 text-blue-500" />
                                <span>{event.capacity} Capacity</span>
                              </div>
                            )}
                          </div>

                          <div className="mt-auto">
                            <button
                              onClick={() => handleRegisterClick(event)}
                              className={`w-full h-10 px-4 py-2 rounded-lg font-medium text-sm transition duration-300 ${userRegistrations[event.id]
                                ? 'bg-green-500 text-white hover:bg-green-600'
                                : 'bg-blue-500 text-white hover:bg-blue-600'}`}
                            >
                              {userRegistrations[event.id] ? 'Registered (Click again to cancel)' : 'Register Now'}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </section>
        </>
      )}

      {/* Registration Modal */}
      {showRegistrationModal && selectedEvent && (
        <EventRegistrationModal
          eventId={selectedEvent.id}
          eventTitle={selectedEvent.title}
          onClose={() => {
            setShowRegistrationModal(false);
            restoreScrollPosition();
          }}
          onSuccess={handleRegistrationSuccess}
        />
      )}

      {/* Success Modal */}
      {showSuccessModal && selectedEvent && (
        <EventSuccessModal
          eventTitle={selectedEvent.title}
          eventDate={selectedEvent.date}
          eventTime={selectedEvent.time}
          eventLocation={selectedEvent.location}
          onClose={() => {
            setShowSuccessModal(false);
            restoreScrollPosition();
          }}
        />
      )}

      {/* Cancellation Confirmation Modal */}
      {showCancellationModal && selectedEvent && (
        <CancellationConfirmModal
          eventTitle={selectedEvent.title}
          eventDate={selectedEvent.date}
          eventTime={selectedEvent.time}
          eventLocation={selectedEvent.location}
          onConfirm={() => {
            // Close modal first
            setShowCancellationModal(false);
            // Then handle cancellation
            handleCancelRegistration(selectedEvent.id);
          }}
          onCancel={() => {
            setShowCancellationModal(false);
            restoreScrollPosition();
          }}
        />
      )}
    </div>
  );
}