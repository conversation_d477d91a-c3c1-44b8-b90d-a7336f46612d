import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../store/authStore';
import { CreditCard, User, LogOut, Mail, Phone, Building2, MapPin, Camera, AlertCircle, CheckCircle, Trash2, Subscript } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';
import { Modal } from '../components/Modal';
import { Link } from 'react-router-dom';
import { ShoppingCart } from 'lucide-react';
import SubscriptionsList from '../components/SubscriptionsList';

interface Subscription {
  id: string;
  ref: string;
  plan_name: string;
  status: string;
  period: string;
  current_period_end: string;
  price: number;
  cancel_at_period_end: boolean;
  product_id: string;
  deleted: boolean;
}

interface Profile {
  full_name: string;
  avatar_url: string | null;
  phone: string | null;
  company: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  zip_code: string | null;
  bio: string | null;
  title: string | null;
}

// Interface for a single subscription item (product details nested)
interface SubscriptionItem {
  quantity: number;
  products: { // Corresponds to the 'products' table joined via 'subscription_items'
    id: string;
    name: string;
    price: number;
  } | null; // Product might be null if join fails or product deleted
}

// Updated Subscription interface
interface Subscription {
  id: string;
  ref: string;
  status: string;
  period: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  deleted: boolean;
  subscription_items: SubscriptionItem[]; // Array of items
}

export default function Profile() {
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]); // Use updated interface
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelSubscriptionId, setCancelSubscriptionId] = useState<string | null>(null);
  const [profile, setProfile] = useState<Profile>({
    full_name: '',
    avatar_url: null,
    phone: null,
    company: null,
    address: null,
    city: null,
    state: null,
    zip_code: null,
    bio: null,
    title: null
  });
  const [formData, setFormData] = useState<Profile>(profile);
  const [isEditing, setIsEditing] = useState(false);
  const [portalLoading, setPortalLoading] = useState(false);

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    fetchProfile();
    fetchSubscriptions();
  }, [user, navigate]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user?.id)
        .single();

      if (error) throw error;
      if (data) {
        setProfile(data);
        setFormData(data);

        if (!data.avatar_url && user?.user_metadata?.avatar_url) {
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              avatar_url: user.user_metadata.avatar_url,
              updated_at: new Date().toISOString()
            })
            .eq('id', user?.id);

          if (!updateError) {
            setProfile(prev => ({ ...prev, avatar_url: user.user_metadata.avatar_url }));
            setFormData(prev => ({ ...prev, avatar_url: user.user_metadata.avatar_url }));
          }
        }
      }
    } catch (err) {
      setError('Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const fetchSubscriptions = async () => {
    if (!user?.id) {
      console.log("fetchSubscriptions skipped: user ID not available yet.");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // --- Updated Supabase Query ---
      const { data, error, status } = await supabase
        .from('subscriptions')
        .select(`
          id,
          ref,
          status,
          period,
          current_period_end,
          cancel_at_period_end,
          deleted,
          subscription_items (
            quantity,
            products (
              id,
              name,
              price
            )
          )
        `)
        .eq('user_id', user.id)
        // Only fetch subscriptions that haven't been marked as deleted by the user
        .eq('deleted', false) // Filter out deleted subscriptions here
        .order('created_at', { ascending: false });
      // --- End Updated Query ---

      console.log("Supabase subscription fetch status:", status);

      if (error) throw error;

      if (!data) {
        console.warn("Subscription data received is null or undefined. Check RLS policies.");
        setSubscriptions([]); // Set to empty array if no data
        return; // Exit early
      }

      // Data should now match the updated Subscription interface structure
      setSubscriptions(data as Subscription[]); // Cast if necessary after validation

    } catch (err: any) {
      console.error("Subscription fetch error:", err);
      const errorMessage = err.message || 'Failed to load subscriptions. Please check console for details.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.full_name?.trim()) {
      setError('Full name is required');
      return;
    }
    if (formData.full_name.trim().length < 3) {
      setError('Full name must be at least 3 characters');
      return;
    }
    if (/^\d+$/.test(formData.full_name.trim())) {
      setError('Full name cannot be only numbers');
      return;
    }

    if (!formData.title?.trim()) {
      setError('Title is required');
      return;
    }
    if (formData.title.trim().length < 3) {
      setError('Title must be at least 3 characters');
      return;
    }
    if (/^\d+$/.test(formData.title.trim())) {
      setError('Title cannot be only numbers');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ ...formData, updated_at: new Date().toISOString() })
        .eq('id', user?.id);

      if (error) throw error;
      setProfile(formData);
      setSuccess('Profile updated successfully!');
      setIsEditing(false);
    } catch (err: any) {
      setError(err.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return;

    try {
      setLoading(true);
      setError(null);

      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const fileName = `${user?.id}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, { upsert: true });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', user?.id);

      if (updateError) throw updateError;

      setProfile(prev => ({ ...prev, avatar_url: publicUrl }));
      setFormData(prev => ({ ...prev, avatar_url: publicUrl }));
      setSuccess('Avatar updated successfully!');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await supabase.auth.signOut();
    navigate('/');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value === '' ? null : value
    }));
  };

  const handleCancelSubscription = async () => {
    if (!cancelSubscriptionId) return;

    try {
      setLoading(true);
      const { error } = await supabase
        .from('subscriptions')
        .update({ status: 'canceled' })
        .eq('id', cancelSubscriptionId);

      if (error) throw error;

      setSubscriptions(prev =>
        prev.map(sub =>
          sub.id === cancelSubscriptionId
            ? { ...sub, status: 'canceled' }
            : sub
        )
      );
      setSuccess('Subscription canceled successfully!');
    } catch (err) {
      setError('Failed to cancel subscription');
    } finally {
      setLoading(false);
      setShowCancelModal(false);
      setCancelSubscriptionId(null);
    }
  };

  if (!user) return null;

  return (
    <div className="min-h-screen pt-16 bg-gray-50">
      {showCancelModal && (
        <Modal
          title="Confirm Cancellation"
          onClose={() => setShowCancelModal(false)}
          actions={
            <>
              <button
                onClick={() => setShowCancelModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleCancelSubscription}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                disabled={loading}
              >
                {loading ? 'Processing...' : 'Confirm Cancellation'}
              </button>
            </>
          }
        >
          <p>Are you sure you want to cancel this subscription?</p>
          <p className="text-sm text-gray-500 mt-2">
            You'll continue to have access until the end of your billing period.
          </p>
        </Modal>
      )}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="grid md:grid-cols-4 gap-6">
          <div className="space-y-6">
            <div className="bg-white p-6 rounded-xl border border-gray-200">
              <div className="text-center mb-4">
                <div className="relative inline-block mb-4">
                  {profile.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile.full_name || 'User Avatar'}
                      className="w-28 h-28 rounded-full object-cover mx-auto ring-2 ring-blue-100"
                    />
                  ) : (
                    <div className="w-28 h-28 rounded-full bg-gray-100 flex items-center justify-center mx-auto">
                      <User className="h-14 w-14 text-gray-400" />
                    </div>
                  )}
                  <label className="absolute bottom-0 right-0 bg-blue-600 p-2 rounded-full cursor-pointer hover:bg-blue-700 transition-colors shadow-sm">
                    <Camera className="h-4 w-4 text-white" />
                    <input
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleAvatarUpload}
                      disabled={loading} // Use profile loading state
                    />
                  </label>
                </div>
                <h2 className="text-xl font-bold text-gray-800">{profile.full_name || 'User'}</h2>
                <p className="text-gray-500 text-sm mt-1">{profile.title || ''}</p>
              </div>
              <div className="border-t pt-4 text-center text-xs text-gray-400">
                Member since {new Date(user.created_at).toLocaleDateString()}
              </div>
            </div>
            <div className="bg-white p-4 rounded-xl border border-gray-200">
              <button
                onClick={handleLogout}
                className="flex items-center space-x-3 text-gray-700 hover:text-red-600 w-full rounded-lg p-3 hover:bg-red-50 transition-all"
              >
                <LogOut className="h-5 w-5" />
                <span className="font-medium text-sm">Sign Out</span>
              </button>
            </div>
          </div>
          <div className="md:col-span-3 space-y-6">
            {error && (
              <div className="bg-red-50 text-red-600 p-3 rounded-lg flex items-center text-sm border border-red-100">
                <AlertCircle className="h-4 w-4 mr-2" />
                {error}
              </div>
            )}
            {success && (
              <div className="bg-green-50 text-green-600 p-3 rounded-lg flex items-center text-sm border border-green-100">
                <CheckCircle className="h-4 w-4 mr-2" />
                {success}
              </div>
            )}
            <div className="bg-white p-6 rounded-xl border border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800">Profile Information</h2>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm disabled:opacity-50 shadow-sm"
                  disabled={loading} // Use profile loading state
                >
                  {isEditing ? 'Cancel' : 'Edit'}
                </button>
              </div>
              {isEditing ? (
                <form onSubmit={handleProfileUpdate} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700">Full Name *</label>
                      <input
                        type="text"
                        name="full_name"
                        value={formData.full_name || ''}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                        disabled={loading}
                        required
                        minLength={3}
                        pattern=".*[^0-9].*"
                        title="Must contain at least 3 characters and not be only numbers"
                      />
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700">Title *</label>
                      <input
                        type="text"
                        name="title"
                        value={formData.title || ''}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                        disabled={loading}
                        required
                        minLength={3}
                        pattern=".*[^0-9].*"
                        title="Must contain at least 3 characters and not be only numbers"
                      />
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700">Phone</label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone || ''}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                        disabled={loading}
                      />
                    </div>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700">Company</label>
                      <input
                        type="text"
                        name="company"
                        value={formData.company || ''}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                        disabled={loading}
                      />
                    </div>
                  </div>
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700">Bio</label>
                    <textarea
                      name="bio"
                      value={formData.bio || ''}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                      disabled={loading}
                    />
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Address</h3>
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700">Street Address</label>
                      <input
                        type="text"
                        name="address"
                        value={formData.address || ''}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                        disabled={loading}
                      />
                    </div>
                    <div className="grid md:grid-cols-3 gap-6">
                      <div className="space-y-1">
                        <label className="block text-sm font-medium text-gray-700">City</label>
                        <input
                          type="text"
                          name="city"
                          value={formData.city || ''}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                          disabled={loading}
                        />
                      </div>
                      <div className="space-y-1">
                        <label className="block text-sm font-medium text-gray-700">State</label>
                        <input
                          type="text"
                          name="state"
                          value={formData.state || ''}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                          disabled={loading}
                        />
                      </div>
                      <div className="space-y-1">
                        <label className="block text-sm font-medium text-gray-700">ZIP Code</label>
                        <input
                          type="text"
                          name="zip_code"
                          value={formData.zip_code || ''}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 rounded-lg bg-white border border-gray-200 hover:border-gray-300 focus:ring-4 focus:ring-blue-100 focus:outline-none disabled:opacity-50"
                          disabled={loading}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={() => {
                        setIsEditing(false);
                        setFormData(profile);
                      }}
                      disabled={loading}
                      className="px-6 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                    >
                      {loading ? 'Saving...' : 'Save'}
                    </button>
                  </div>
                </form>
              ) : (
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <p className="flex items-center">
                      <Mail className="h-5 w-5 mr-2 text-gray-500" />
                      {user.email}
                    </p>
                    {profile.phone && (
                      <p className="flex items-center">
                        <Phone className="h-5 w-5 mr-2 text-gray-500" />
                        {profile.phone}
                      </p>
                    )}
                    {profile.company && (
                      <p className="flex items-center">
                        <Building2 className="h-5 w-5 mr-2 text-gray-500" />
                        {profile.company}
                      </p>
                    )}
                  </div>
                  <div className="space-y-4">
                    {profile.bio && <p>{profile.bio}</p>}
                    {(profile.address || profile.city) && (
                      <p className="flex items-start">
                        <MapPin className="h-5 w-5 mr-2 text-gray-500 flex-shrink-0" />
                        {profile.address}{profile.address && profile.city && ', '}
                        {profile.city}{profile.city && profile.state && ', '}
                        {profile.state} {profile.zip_code}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
            <SubscriptionsList />
          </div>
        </div>
      </div>
    </div>
  );
}
