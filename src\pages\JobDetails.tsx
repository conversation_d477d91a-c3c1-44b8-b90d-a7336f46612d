import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Briefcase, Users, Award, Building2, ArrowLeft, ArrowRight } from 'lucide-react';

interface JobListing {
  id: string;
  title: string;
  department: string;
  location: string;
  type: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary_range: {
    min: number;
    max: number;
    currency: string;
  } | null;
}

export default function JobDetails() {
  const { id } = useParams<{ id: string }>();
  const [job, setJob] = useState<JobListing | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchJob = async () => {
      setLoading(true);
      const { data, error } = await supabase
        .from('job_listings')
        .select('*')
        .eq('id', id)
        .single();
      if (!error) setJob(data);
      setLoading(false);
    };
    fetchJob();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[40vh]">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="max-w-2xl mx-auto py-20 text-center">
        <Briefcase className="h-12 w-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Job Not Found</h2>
        <p className="text-gray-600 mb-6">The job you are looking for does not exist or is no longer available.</p>
        <Link
          to="/careers"
          className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
        >
          <ArrowLeft className="mr-2 h-5 w-5" />
          Back to Careers
        </Link>
      </div>
    );
  }

  return (
    <div className="pt-16 pb-20 bg-gray-50 min-h-screen">
      <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-lg p-8 mt-10">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Briefcase className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold">{job.title}</h1>
          </div>
          <span className="px-4 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            {job.type}
          </span>
        </div>
        <div className="flex flex-wrap gap-6 mb-6">
          <div className="flex items-center text-gray-700">
            <Building2 className="h-5 w-5 mr-2" />
            <span>{job.department}</span>
          </div>
          <div className="flex items-center text-gray-700">
            <Users className="h-5 w-5 mr-2" />
            <span>{job.location}</span>
          </div>
          {job.salary_range && (
            <div className="flex items-center text-gray-700">
              <Award className="h-5 w-5 mr-2" />
              <span>
                {job.salary_range.currency} {job.salary_range.min.toLocaleString()} - {job.salary_range.max.toLocaleString()} / year
              </span>
            </div>
          )}
        </div>
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Job Description</h2>
          <p className="text-gray-800">{job.description}</p>
        </div>
        {job.requirements && job.requirements.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-2">Requirements</h2>
            <ul className="list-disc list-inside text-gray-800 space-y-1">
              {job.requirements.map((req, idx) => (
                <li key={idx}>{req}</li>
              ))}
            </ul>
          </div>
        )}
        {job.responsibilities && job.responsibilities.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-2">Responsibilities</h2>
            <ul className="list-disc list-inside text-gray-800 space-y-1">
              {job.responsibilities.map((resp, idx) => (
                <li key={idx}>{resp}</li>
              ))}
            </ul>
          </div>
        )}
        {job.benefits && job.benefits.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-2">Benefits</h2>
            <ul className="list-disc list-inside text-gray-800 space-y-1">
              {job.benefits.map((benefit, idx) => (
                <li key={idx}>{benefit}</li>
              ))}
            </ul>
          </div>
        )}
        <div className="flex gap-4 mt-8">
          <Link
            to="/careers"
            className="inline-flex items-center bg-gray-100 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Careers
          </Link>
          <Link
            to={`/careers/apply/${job.id}`}
            className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
          >
            Apply Now
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </div>
  );
}