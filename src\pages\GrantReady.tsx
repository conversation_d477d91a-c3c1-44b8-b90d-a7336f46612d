import { useState, useEffect, useRef } from 'react';
import { Users, Zap, CheckCircle, Brain, Check, Play, Cloud, Shield, Settings, Sparkles, BookOpen, Calendar, HelpCircle, FileQuestion, ShoppingCart, X, ExternalLink } from 'lucide-react';
import { useCartStore } from '../store/cartStore';
import { useAuthStore } from '../store/authStore';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import grantReadyFeats from '../assets/images/grantready-feats.png';
import grantReadyPreviewThumbnail from '../assets/images/GrantReady-Preview-Thumbnail.jpg';
import booksIcon from '../assets/images/books-icon.png';
import fastTruck from '../assets/images/fast-truck.png';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages?: string[];
  specifications: string[];
  theme_color?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
  updated_at?: string;
}

export default function GrantReady() {
  const { addItem } = useCartStore();
  const user = useAuthStore((state) => state.user);
  const [products, setProducts] = useState<Product[]>([]);
  // Loading state is set in fetchProducts but not currently used in UI
  const setLoading = useState(true)[1];
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  const [expandedDescriptions, setExpandedDescriptions] = useState<{ [key: string]: boolean }>({});
  const [addedProducts, setAddedProducts] = useState<{ [key: string]: boolean }>({});
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [maxYearlyDiscount, setMaxYearlyDiscount] = useState<number>(0);
  const videoRef = useRef<HTMLVideoElement>(null);

  // No longer need the Plan type since we're using direct product data

  const features = [
    {
      icon: () => (
        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="32" cy="32" r="28" stroke="#fff" strokeWidth="4" fill="none" />
          <text x="32" y="42" textAnchor="middle" fontSize="32" fontWeight="bold" fill="#fff">50</text>
        </svg>
      ),
      title: 'Experience',
      description: 'Years of experience in public health emergency preparedness and response'
    },
    {
      icon: () => (
        <img
          src={booksIcon}
          alt="Books Icon"
          className="w-24 h-24 rounded-full object-contain bg-transparent shadow-none border-0"
        />
      ),
      title: 'Knowledge',
      description: 'Extensive knowledge in government, healthcare, engineering disaster & emergency response'
    },
    {
      icon: Sparkles,
      title: 'Design',
      description: 'Intuitive software design for flexibility, responsiveness, and automation'
    },
    { icon: Shield, title: 'secure', description: 'Enterprise-grade security protocols' },
    { icon: Zap, title: 'Performance', description: 'Lightning-fast processing' },
    { icon: Brain, title: 'All-in-One Platform', description: 'Everything you need in one place' },
    { icon: Users, title: 'More Control', description: 'Enhanced user management' },
    { icon: CheckCircle, title: 'Support & Monitoring', description: '24/7 system monitoring' }
  ];

  useEffect(() => {
    fetchProducts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchProducts = async () => {
    try {
      console.log('Fetching products for the GrantReady page');

      // First, let's get all products to examine their structure
      const { data: allProducts, error: fetchError } = await supabase
        .from('products')
        .select('*');

      if (fetchError) {
        console.error('Error fetching products:', fetchError);
        setProducts([]);
        console.error(`Error fetching products: ${fetchError.message || fetchError}`);
        setLoading(false);
        return;
      }

      console.log('All products from database:', allProducts);

      // Log the structure of the pages field for debugging
      if (allProducts && allProducts.length > 0) {
        console.log('First product pages field:', allProducts[0].pages);
        console.log('Type of pages field:', typeof allProducts[0].pages);
        if (Array.isArray(allProducts[0].pages)) {
          console.log('Pages is an array with length:', allProducts[0].pages.length);
        } else if (typeof allProducts[0].pages === 'object') {
          console.log('Pages is an object with keys:', Object.keys(allProducts[0].pages));
        }
      }

      // Filter products client-side for now to get past the error
      const grantReadyProducts = allProducts.filter(product => {
        // Check if pages exists and is an array
        if (Array.isArray(product.pages)) {
          return product.pages.includes('GrantReady');
        }
        // If pages is a string (JSON string), try to parse it
        if (typeof product.pages === 'string') {
          try {
            const pagesArray = JSON.parse(product.pages);
            return Array.isArray(pagesArray) && pagesArray.includes('GrantReady');
          } catch (e) {
            console.error('Error parsing pages JSON:', e);
            return false;
          }
        }
        return false;
      });

      console.log('Filtered GrantReady products:', grantReadyProducts);

      // If we have products for GrantReady
      if (grantReadyProducts && grantReadyProducts.length > 0) {
        // Sort by price to ensure consistent order
        const sortedProducts = [...grantReadyProducts].sort((a, b) => (a.price || 0) - (b.price || 0));

        // Log the sorted products with formatted prices for debugging
        console.log('Sorted GrantReady products:', sortedProducts.map(p => ({
          id: p.id,
          name: p.name,
          price: p.price.toFixed(2),
          monthly_discount: p.monthly_discount || 0,
          yearly_discount: p.yearly_discount || 0
        })));

        // Find the maximum yearly discount among all products
        const highestDiscount = sortedProducts.reduce((max, product) => {
          const discount = product.yearly_discount || 0;
          return discount > max ? discount : max;
        }, 0);

        setMaxYearlyDiscount(highestDiscount);
        setProducts(sortedProducts);
      } else {
        // If there are no products for GrantReady, set empty array
        console.log('No products found for GrantReady page');
        setProducts([]);
      }
    } catch (err: unknown) {
      console.error('Error fetching GrantReady products:', err);
      console.error(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  /* Removed unused function that was replaced with direct addItem calls */

  const toggleDescription = (productId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  const closeVideoModal = () => {
    if (videoRef.current) {
      videoRef.current.pause();
    }
    setIsVideoModalOpen(false);
  };

  // Function to truncate text
  const truncateText = (text: string, wordCount: number = 12) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordCount) return text;
    return words.slice(0, wordCount).join(' ') + '...';
  };

  // Function to get color classes based on theme color
  const getColorClasses = (themeColor: string = 'purple') => {
    const colorMap = {
      blue: {
        gradient: 'from-blue-600 via-blue-700 to-blue-800',
        gradientLight: 'from-blue-400/30',
        gradientMedium: 'from-blue-500/30',
        gradientOverlay: 'from-blue-600/80 via-blue-700/80 to-blue-800/80',
        button: 'bg-blue-600 hover:bg-blue-700',
        border: 'border-blue-600',
        hoverBorder: 'hover:border-blue-100',
        text: 'text-blue-600',
        badge: 'bg-blue-100 text-blue-800',
        highlight: 'bg-blue-600'
      },
      orange: {
        gradient: 'from-orange-600 via-orange-700 to-orange-800',
        gradientLight: 'from-orange-400/30',
        gradientMedium: 'from-orange-500/30',
        gradientOverlay: 'from-orange-600/80 via-orange-700/80 to-orange-800/80',
        button: 'bg-orange-600 hover:bg-orange-700',
        border: 'border-orange-600',
        hoverBorder: 'hover:border-orange-100',
        text: 'text-orange-600',
        badge: 'bg-orange-100 text-orange-800',
        highlight: 'bg-orange-600'
      },
      purple: {
        gradient: 'from-[#6358A6] via-[#6358A6] to-[#4673B7]',
        gradientLight: 'from-[#6358A6]/30',
        gradientMedium: 'from-[#4673B7]/30',
        gradientOverlay: 'from-[#6358A6]/80 via-[#6358A6]/80 to-[#4673B7]/80',
        button: 'bg-[#6358A6] hover:bg-[#4673B7]',
        border: 'border-[#6358A6]',
        hoverBorder: 'hover:border-[#6358A6]/20',
        text: 'text-[#6358A6]',
        badge: 'bg-purple-100 text-purple-800',
        highlight: 'bg-[#6358A6]'
      },
      yellow: {
        gradient: 'from-yellow-600 via-yellow-700 to-yellow-800',
        gradientLight: 'from-yellow-400/30',
        gradientMedium: 'from-yellow-500/30',
        gradientOverlay: 'from-yellow-600/80 via-yellow-700/80 to-yellow-800/80',
        button: 'bg-yellow-600 hover:bg-yellow-700',
        border: 'border-yellow-600',
        hoverBorder: 'hover:border-yellow-100',
        text: 'text-yellow-600',
        badge: 'bg-yellow-100 text-yellow-800',
        highlight: 'bg-yellow-600'
      }
    };

    return colorMap[themeColor as keyof typeof colorMap] || colorMap.purple;
  };

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[#6358A6] via-[#6358A6] to-[#4673B7] text-white py-8 sm:py-10 md:py-12 lg:py-16 overflow-hidden">
        {/* Background Effects Container */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated Gradients */}
          <div 
            className="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] rounded-full bg-gradient-to-br from-[#6358A6]/30 to-transparent blur-3xl transform-gpu animate-[spin_30s_linear_infinite]"
            aria-hidden="true"
          />
          <div 
            className="absolute -bottom-1/2 -right-1/2 w-[200%] h-[200%] rounded-full bg-gradient-to-br from-[#4673B7]/30 to-transparent blur-3xl transform-gpu animate-[spin_25s_linear_infinite_reverse]"
            aria-hidden="true"
          />

          {/* Grid Pattern */}
          <div
            className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiIGlkPSJhIj48c3RvcCBzdG9wLWNvbG9yPSIjZmZmIiBzdG9wLW9wYWNpdHk9Ii4yIiBvZmZzZXQ9IjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iI2ZmZiIgc3RvcC1vcGFjaXT5PSIwIiBvZmZzZXQ9IjEwMCUiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cGF0aCBkPSJNMCAwaDYwdjYwSDB6IiBmaWxsPSJ1cmwoI2EpIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=')]
            opacity-20 transform-gpu animate-[gradient_15s_ease_infinite]"
            aria-hidden="true"
          />

          {/* Overlay */}
          <div 
            className="absolute inset-0 bg-gradient-to-br from-[#6358A6]/80 via-[#6358A6]/80 to-[#4673B7]/80 backdrop-blur-[2px]"
            aria-hidden="true"
          />
        </div>

        {/* Content Container */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left Column - Text Content */}
            <div className="animate-[fadeIn_1s_ease-out] space-y-6 max-w-xl mx-auto lg:mx-0">
              <div>
                <span className="inline-block px-3 py-1 rounded-full bg-white/20 backdrop-blur-sm text-sm font-medium border border-white/10">
                  International Responder Systems GrantReady™
                </span>
              </div>
              
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold [text-shadow:_0_1px_2px_rgb(0_0_0_/_20%)] leading-tight">
                Simplify Grant<br className="hidden sm:block" /> Management
              </h1>
              
              <p className="text-base sm:text-lg lg:text-xl text-white/90 max-w-lg">
                Radically redefine the way local health jurisdictions (LHJ) and states plan, execute, and monitor federal funding with International Responder Systems GrantReady™.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col space-y-4 sm:space-y-6 w-full max-w-md">
                <a
                  href="https://grantready.internationalrespondersystems.net/login/?redirect-path=%2F"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full bg-white text-[#6358A6] px-3 sm:px-4 py-3 sm:py-4 rounded-lg font-bold text-sm sm:text-base whitespace-nowrap hover:bg-[#6358A6]/10 hover:text-white hover:border-white border-2 border-transparent transition-all duration-300"
                >
                  International Responder Systems GrantReady™
                </a>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 w-full">
                  {[
                    { to: "/solutions/grantready/whitepaper", text: "Read Whitepaper" },
                    { to: "/support", text: "Support Center" },
                    { to: "/contact", text: "Contact Us" }
                  ].map((link) => (
                    <Link
                      key={link.to}
                      to={link.to}
                      className="border-2 border-white text-white px-3 py-2.5 rounded-lg font-semibold hover:bg-white/10 transition-all duration-300 text-center text-sm sm:text-base flex items-center justify-center min-w-[130px] h-full"
                    >
                      {link.text}
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column - Video Preview */}
            <div className="animate-[slideIn_1s_ease-out] mt-8 lg:mt-0">
              <div className="relative max-w-xl mx-auto lg:mx-0">
                <div className="bg-white/10 backdrop-blur-md p-3 sm:p-4 lg:p-6 rounded-xl transform hover:scale-[1.02] transition-all duration-300 hover:shadow-2xl">
                  <button
                    onClick={() => setIsVideoModalOpen(true)}
                    className="relative w-full aspect-video rounded-lg overflow-hidden group cursor-pointer"
                  >
                    <img
                      src={grantReadyPreviewThumbnail}
                      alt="GrantReady Preview"
                      className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors duration-300 flex items-center justify-center backdrop-blur-[2px]">
                      <div className="flex items-center transform group-hover:scale-110 transition-transform duration-300">
                        <Play className="h-6 w-6 sm:h-8 sm:w-8 lg:h-12 lg:w-12" />
                        <span className="ml-2 text-sm sm:text-base lg:text-lg font-medium">Watch Preview</span>
                      </div>
                    </div>
                  </button>
                </div>

                {/* Floating "See All Modules" Button */}
                <div className="absolute bottom-0 right-0 translate-y-8 sm:translate-y-12">
                  <a
                    href="https://docs.internationalrespondersystems.net/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded-full text-white text-sm font-medium inline-flex items-center transition-all duration-300 hover:bg-white/30 hover:shadow-lg group border border-white/30"
                  >
                    <span>See All Modules</span>
                    <ExternalLink className="ml-1.5 h-4 w-4 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Illustration Section - New section below hero */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="flex justify-center order-last md:order-first">
              <div className="relative">
                <div className="absolute -inset-4 bg-[#6358A6]/5 rounded-full blur-lg"></div>
                <img
                  src={grantReadyFeats}
                  alt="International Responder Systems GrantReady™ Features"
                  className="relative z-10 w-80 h-80 object-contain"
                />
              </div>
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-6 text-gray-900">Streamline Your Grant Process</h2>
              <p className="text-xl text-gray-600 mb-8">
                International Responder Systems GrantReady™ helps health jurisdictions manage federal funding with ease. Our platform is designed specifically for PHEP, HPP, FOPH, and CASPHI grants.
              </p>
              <div className="flex flex-wrap">
                <a
                  href="https://grantready.internationalrespondersystems.net/login/?redirect-path=%2F"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#6358A6] text-white px-8 py-4 rounded-lg font-semibold hover:bg-[#4673B7] transition-all duration-300 text-lg"
                >
                  Try Now
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 relative bg-gradient-to-br from-[#6358A6] via-[#7C6FC7] to-[#463B7B] text-white shadow-xl w-full mx-0 mt-8 border-none overflow-hidden">
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-1/2 -left-1/2 w-full h-full rounded-full bg-gradient-to-br from-[#7C6FC7]/30 to-transparent blur-3xl animate-[spin_30s_linear_infinite]"></div>
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full rounded-full bg-gradient-to-br from-[#463B7B]/30 to-transparent blur-3xl animate-[spin_25s_linear_infinite_reverse]"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-[#6358A6]/80 via-[#7C6FC7]/80 to-[#463B7B]/80 backdrop-blur-[2px] opacity-90"></div>
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">International Responder Systems GrantReady™ Features</h2>
            <p className="text-xl text-white/80">
              Our cloud-based solution offers quality support and monitoring, ensuring your grant management is always secure and efficient.
            </p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="p-6 bg-white/10 rounded-xl hover:shadow-2xl transition-shadow flex flex-col items-center text-center border border-white/10">
                <feature.icon className="h-12 w-12 text-[#E0D7F7] mb-2" />
                {feature.title && <h3 className="text-lg font-semibold mb-2 text-white">{feature.title}</h3>}
                <p className="text-white/90 text-base">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">Choose Your Plan</h2>
            <p className="text-xl text-gray-600 mb-8">Start managing grants more efficiently with International Responder Systems GrantReady™</p>

            <div className="bg-white/10 backdrop-blur-sm p-2 rounded-lg inline-flex mb-8 shadow-sm">
              <button
                onClick={() => setBillingInterval('monthly')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'monthly'
                  ? 'bg-[#6358A6] text-white'
                  : 'text-gray-700 hover:bg-gray-100'
                  }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingInterval('yearly')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'yearly'
                  ? 'bg-[#6358A6] text-white'
                  : 'text-gray-700 hover:bg-gray-100'
                  }`}
              >
                Yearly
                {maxYearlyDiscount > 0 && (
                  <span className="ml-2 text-sm bg-green-500 text-white px-2 py-1 rounded-full">
                    Save up to {maxYearlyDiscount}%
                  </span>
                )}
              </button>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {products.length > 0 ? (
              // If we have products from the database, display them directly
              products.map((product) => (
                <div key={product.id} className={`bg-white rounded-lg shadow-lg overflow-hidden border ${getColorClasses(product.theme_color).hoverBorder}`}>
                  <div className="p-8">
                    <h3 className="text-2xl font-bold mb-2">{product.name}</h3>
                    <div className="text-gray-600 mb-4 min-h-[4.5rem]">
                      {expandedDescriptions[product.id] ? (
                        <>
                          <p>{product.description}</p>
                          <button
                            onClick={() => toggleDescription(product.id)}
                            className={`${getColorClasses(product.theme_color).text} text-sm mt-2 hover:underline focus:outline-none`}
                          >
                            Read less
                          </button>
                        </>
                      ) : (
                        <>
                          <p>{truncateText(product.description)}</p>
                          {product.description && product.description.split(' ').length > 12 && (
                            <button
                              onClick={() => toggleDescription(product.id)}
                              className={`${getColorClasses(product.theme_color).text} text-sm mt-1 hover:underline focus:outline-none`}
                            >
                              Read more
                            </button>
                          )}
                        </>
                      )}
                    </div>
                    <div className="mb-8 h-14 flex items-end">
                      <div>
                        {billingInterval === 'yearly' ? (
                          product.yearly_discount ? (
                            <div className="flex flex-col">
                              <div className="flex items-center">
                                <span className="text-4xl font-bold">
                                  ${((product.price * 12) * (1 - product.yearly_discount / 100)).toFixed(2)}
                                </span>
                                <span className="text-gray-600">/year</span>
                              </div>
                              <div className="flex items-center">
                                <span className="text-sm text-gray-500 line-through mr-2">
                                  ${(product.price * 12).toFixed(2)}
                                </span>
                                <span className="text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                                  Save {product.yearly_discount}%
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div>
                              <span className="text-4xl font-bold">
                                ${(product.price * 12).toFixed(2)}
                              </span>
                              <span className="text-gray-600">/year</span>
                            </div>
                          )
                        ) : (
                          product.monthly_discount ? (
                            <div className="flex flex-col">
                              <div className="flex items-center">
                                <span className="text-4xl font-bold">
                                  ${(product.price * (1 - product.monthly_discount / 100)).toFixed(2)}
                                </span>
                                <span className="text-gray-600">/month</span>
                              </div>
                              <div className="flex items-center">
                                <span className="text-sm text-gray-500 line-through mr-2">
                                  ${product.price.toFixed(2)}
                                </span>
                                <span className="text-sm bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                  Save {product.monthly_discount}%
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div>
                              <span className="text-4xl font-bold">
                                ${product.price.toFixed(2)}
                              </span>
                              <span className="text-gray-600">/month</span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                    {user ? (
                      <button
                        onClick={() => {
                          // Add item to cart with billing period
                          addItem(product.id, billingInterval);

                          // Set this product as recently added
                          setAddedProducts(prev => ({
                            ...prev,
                            [product.id]: true
                          }));

                          // Reset the added state after 800 milliseconds
                          setTimeout(() => {
                            setAddedProducts(prev => ({
                              ...prev,
                              [product.id]: false
                            }));
                          }, 800);
                        }}
                        className={`w-full ${addedProducts[product.id] ? 'bg-green-600 hover:bg-green-700' : getColorClasses(product.theme_color).button} text-white py-3 px-6 rounded-lg font-semibold transition duration-300`}
                        disabled={addedProducts[product.id]}
                      >
                        {addedProducts[product.id] ? (
                          <>
                            <Check className="inline-block h-5 w-5 mr-2" />
                            Added to Cart
                          </>
                        ) : (
                          <>
                            <ShoppingCart className="inline-block h-5 w-5 mr-2" />
                            Add to Cart
                          </>
                        )}
                      </button>
                    ) : (
                      <Link
                        to="/signup"
                        className={`block w-full text-center ${getColorClasses(product.theme_color).button} text-white py-3 px-6 rounded-lg font-semibold transition duration-300`}
                      >
                        Sign Up to Subscribe
                      </Link>
                    )}
                  </div>
                  <div className="p-8 border-t border-gray-100">
                    <ul className="space-y-4">
                      {product.specifications.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <Check className={`h-5 w-5 ${getColorClasses(product.theme_color).text} mr-2`} />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))
            ) : (
              // If no products found, display a message
              <div className="col-span-3 text-center py-12">
                <div className="bg-gray-50 rounded-lg p-8 max-w-md mx-auto">
                  <h3 className="text-xl font-bold mb-4 text-gray-700">No Products Available</h3>
                  <p className="text-gray-600 mb-6">
                    There are currently no GrantReady products available. Please check back later or contact our sales team for more information.
                  </p>
                  <Link
                    to="/contact"
                    className="inline-block bg-[#6358A6] text-white py-2 px-6 rounded-lg font-semibold hover:bg-[#4673B7] transition duration-300"
                  >
                    Contact Sales
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Accelerate Data Section */}
      <section className="py-20 relative bg-gradient-to-br from-[#6358A6] via-[#7C6FC7] to-[#463B7B] text-white shadow-xl w-full mx-0 mt-8 border-none overflow-hidden">
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-1/2 -left-1/2 w-full h-full rounded-full bg-gradient-to-br from-[#7C6FC7]/30 to-transparent blur-3xl animate-[spin_30s_linear_infinite]"></div>
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full rounded-full bg-gradient-to-br from-[#463B7B]/30 to-transparent blur-3xl animate-[spin_25s_linear_infinite_reverse]"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-[#6358A6]/80 via-[#7C6FC7]/80 to-[#463B7B]/80 backdrop-blur-[2px] opacity-90"></div>
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold mb-6 text-white">Accelerate Data,<br/>Amplify Action</h2>
              <p className="text-lg text-white/90 max-w-xl">
                International Responders Systems GrantReady™ ensures that the users can use data in a faster, more interoperable way that, in turn, provides high-quality information that provides a more real-time, comprehensive picture to improve decision-making and protect health.
              </p>
            </div>
            <div className="flex justify-center">
              <img
                src={fastTruck}
                alt="Ambulance speeding"
                className="rounded-2xl shadow-2xl w-full max-w-md object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Resources Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">We Are Here to Help</h2>
            <p className="text-xl text-gray-600">Access our comprehensive resources</p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            <Link to="/faq" className="group p-6 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow text-center">
              <HelpCircle className="h-8 w-8 text-[#6358A6] mx-auto mb-4" />
              <h3 className="text-lg font-semibold group-hover:text-[#6358A6]">FAQ</h3>
            </Link>
            <Link to="/guides" className="group p-6 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow text-center">
              <FileQuestion className="h-8 w-8 text-[#6358A6] mx-auto mb-4" />
              <h3 className="text-lg font-semibold group-hover:text-[#6358A6]">Guides</h3>
            </Link>
            <Link to="/events" className="group p-6 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow text-center">
              <Calendar className="h-8 w-8 text-[#6358A6] mx-auto mb-4" />
              <h3 className="text-lg font-semibold group-hover:text-[#6358A6]">Events</h3>
            </Link>
            <Link to="/products" className="group p-6 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow text-center">
              <BookOpen className="h-8 w-8 text-[#6358A6] mx-auto mb-4" />
              <h3 className="text-lg font-semibold group-hover:text-[#6358A6]">Pricing</h3>
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-[#6358A6] text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Grant Management?</h2>
          <p className="text-xl mb-8">Get in touch with our team to learn how International Responder Systems GrantReady™ can help.</p>
          <Link
            to="/contact"
            className="inline-flex items-center bg-white text-[#6358A6] px-6 py-3 rounded-lg font-semibold hover:bg-[#6358A6]/10 hover:text-white hover:border-white border-2 border-transparent transition-all duration-300"
          >
            Contact Us
          </Link>
        </div>
      </section>

      {/* Video Modal */}
      {isVideoModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
          onClick={closeVideoModal}
        >
          <div
            className="relative bg-black rounded-lg overflow-hidden max-w-4xl w-full mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-end p-2 absolute top-0 right-0 z-10">
              <button
                onClick={closeVideoModal}
                className="bg-black/50 hover:bg-black/70 text-white rounded-full p-1 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="aspect-video w-full">
              <video
                ref={videoRef}
                src="https://docs.internationalrespondersystems.net/assets/videos/module_01.mp4"
                className="w-full h-full"
                controls
                autoPlay
              >
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
