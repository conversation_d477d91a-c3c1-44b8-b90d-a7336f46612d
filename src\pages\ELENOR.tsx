import React, { useState, useEffect } from 'react';
import { Activity, Mail, Brain, Shield, Clock, Users, Zap, Lock, ShoppingCart, Check } from 'lucide-react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import ProductDisplay from '../components/ProductDisplay';
import irsLogo from '../assets/images/irs-icon-rb-light.png';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages?: string[];
  specifications: string[];
  theme_color?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
  updated_at?: string;
}

export default function ELENOR() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  const [maxYearlyDiscount, setMaxYearlyDiscount] = useState<number>(0);

  useEffect(() => {
    fetchProducts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchProducts = async () => {
    try {
      console.log('Fetching products for the ELENOR page');

      // First, let's get all products to examine their structure
      const { data: allProducts, error: fetchError } = await supabase
        .from('products')
        .select('*');

      if (fetchError) {
        console.error('Error fetching products:', fetchError);
        setProducts([]);
        setError(`Error fetching products: ${fetchError.message || fetchError}`);
        setLoading(false);
        return;
      }

      console.log('All products from database:', allProducts);

      // Filter products client-side for ELENOR page
      const elenorProducts = allProducts.filter(product => {
        // Check if pages exists and is an array
        if (Array.isArray(product.pages)) {
          return product.pages.includes('Elenor');
        }
        // If pages is a string (JSON string), try to parse it
        if (typeof product.pages === 'string') {
          try {
            const pagesArray = JSON.parse(product.pages);
            return Array.isArray(pagesArray) && pagesArray.includes('Elenor');
          } catch (e) {
            console.error('Error parsing pages JSON:', e);
            return false;
          }
        }
        return false;
      });

      console.log('Filtered ELENOR products:', elenorProducts);

      // If we have products for ELENOR page
      if (elenorProducts && elenorProducts.length > 0) {
        // Sort by price to ensure consistent order
        const sortedProducts = [...elenorProducts].sort((a, b) => (a.price || 0) - (b.price || 0));

        // Log the sorted products with formatted prices for debugging
        console.log('Sorted ELENOR products:', sortedProducts.map(p => ({
          id: p.id,
          name: p.name,
          price: p.price.toFixed(2),
          monthly_discount: p.monthly_discount || 0,
          yearly_discount: p.yearly_discount || 0
        })));

        // Find the maximum yearly discount among all products
        const highestDiscount = sortedProducts.reduce((max, product) => {
          const discount = product.yearly_discount || 0;
          return discount > max ? discount : max;
        }, 0);

        setMaxYearlyDiscount(highestDiscount);
        setProducts(sortedProducts);
      } else {
        // If there are no products for ELENOR page, set empty array
        console.log('No products found for ELENOR page');
        setProducts([]);
      }
    } catch (err: unknown) {
      console.error('Error fetching products:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const features = [
    { icon: Brain, title: 'AI-Powered Analysis', description: 'Advanced machine learning for real-time situation assessment' },
    { icon: Shield, title: 'Secure Platform', description: 'Enterprise-grade security for sensitive emergency data' },
    { icon: Clock, title: 'Real-time Updates', description: 'Instant notifications and status changes' },
    { icon: Users, title: 'Team Coordination', description: 'Seamless communication between response teams' },
    { icon: Zap, title: 'Quick Response', description: 'Rapid deployment of emergency protocols' },
    { icon: Lock, title: 'Access Control', description: 'Role-based permissions and authentication' }
  ];

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-yellow-500 to-yellow-600 text-white py-20 sm:py-24 lg:py-28 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0MCIgaGVpZ2h0PSI1MDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IHgxPSIxMDAlIiB5MT0iMjEuMTgyJSIgeDI9IjUwJSIgeTI9IjEwMCUiIGlkPSJhIj48c3RvcCBzdG9wLWNvbG9yPSIjRkZGIiBvZmZzZXQ9IjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iI0ZGRiIgc3RvcC1vcGFjaXR5PSIwIiBvZmZzZXQ9IjEwMCUiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cGF0aCBkPSJNLTM1MS41IDUwMS41bDE4OTgtMTA5NiIgc3Ryb2tlPSJ1cmwoI2EpIiBzdHJva2Utd2lkdGg9IjEuNSIgZmlsbD0ibm9uZSIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJNLTM4MS41IDUwMS41bDE4OTgtMTA5NiIgc3Ryb2tlPSJ1cmwoI2EpIiBzdHJva2Utd2lkdGg9IjEuNSIgZmlsbD0ibm9uZSIgb3BhY2l0eT0iLjQiLz48L3N2Zz4=')] animate-[gradient_15s_ease_infinite]"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/90 to-yellow-600/90"></div>
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <Activity className="h-14 w-14 text-white mx-auto mb-5" />
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-5 [text-shadow:_0_1px_2px_rgb(0_0_0_/_20%)]">
            Situational Awareness at your Fingertips
          </h1>
          <p className="text-base md:text-lg lg:text-xl mb-8 text-yellow-50 max-w-2xl mx-auto">
            We are working hard to build this new site experience for you!
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center bg-white text-yellow-600 px-7 py-3 rounded-lg font-semibold hover:bg-yellow-50 transition duration-300"
          >
            <Mail className="mr-2 h-5 w-5" />
            Contact Us
          </Link>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Features</h2>
            <p className="text-xl text-gray-600">
              Advanced emergency response and coordination platform
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="p-6 bg-gray-50 rounded-xl hover:shadow-lg transition-shadow">
                <feature.icon className="h-8 w-8 text-yellow-500 mb-4" />
                <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Solutions</h2>
            <p className="text-xl text-gray-600 mb-8">Choose the perfect plan for your organization</p>

            <div className="bg-white/10 backdrop-blur-sm p-2 rounded-lg inline-flex mb-8 shadow-sm">
              <button
                onClick={() => setBillingInterval('monthly')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'monthly'
                  ? 'bg-yellow-600 text-white'
                  : 'text-gray-700 hover:bg-gray-100'
                  }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingInterval('yearly')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'yearly'
                  ? 'bg-yellow-600 text-white'
                  : 'text-gray-700 hover:bg-gray-100'
                  }`}
              >
                Yearly
                {maxYearlyDiscount > 0 && (
                  <span className="ml-2 text-sm bg-green-500 text-white px-2 py-1 rounded-full">
                    Save up to {maxYearlyDiscount}%
                  </span>
                )}
              </button>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
            </div>
          ) : (
            <ProductDisplay products={products} pageName="ELENOR" billingInterval={billingInterval} />
          )}
        </div>
      </section>
    </div>
  );
}