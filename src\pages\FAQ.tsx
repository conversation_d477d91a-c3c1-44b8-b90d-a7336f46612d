import React, { useState } from 'react';
import { ChevronDown, Key, Lock, UserCog, Users, Settings, UserX, UserCheck, HelpCircle, Lightbulb, MessageSquareWarning, BookOpen, MessageCircleQuestion, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';
import FAQIMG from '../assets/images/faq.jpg'

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  icon: React.ElementType;
}

export default function FAQ() {
  const [openItem, setOpenItem] = useState<string | null>(null);

  const faqItems: FAQItem[] = [
    {
      id: '01',
      icon: Key,
      question: 'How do I get access to International Responders Systems GrantReady™?',
      answer: 'The system administrator assigned to your organization will create an account in the International Responders Systems GrantReady™ for you. International Responder Systems GrantReady™ will send an email to each user from the system.'
    },
    {
      id: '02',
      icon: Lock,
      question: 'Do I need to set up the two step authentication every time I am going to access International Responders Systems GrantReady™?',
      answer: 'No. You only need to set it up once with the authenticator app of your choice.'
    },
    {
      id: '03',
      icon: UserCog,
      question: 'What can the system administrators from my organization do?',
      answer: 'System administrators from your organization can set up the roles for your organization as well as invite new users. These users can also add new organizations to collaborate with in the system.'
    },
    {
      id: '04',
      icon: Settings,
      question: 'Are the user roles set by International Responders Systems GrantReady™?',
      answer: 'No. Setting up the roles for your team members is entirely up to the system administrators from your organization.'
    },
    {
      id: '05',
      icon: Users,
      question: 'What types of permissions can a specific role have in my organization\'s information?',
      answer: 'When setting up the roles, the system administrators can grant permissions to users to only view documents, work (edit) on a document and/or approve a specific document.'
    },
    {
      id: '06',
      icon: UserX,
      question: 'What happens when a user no longer works for the organization, is the data they entered lost?',
      answer: 'No, the data stays in International Responders Systems GrantReady™. Deactivating the user account does not affect the information in the system.'
    },
    {
      id: '07',
      icon: UserCheck,
      question: 'What happens if I change positions and now need to work on other documents in International Responders Systems GrantReady™?',
      answer: 'Your organization\'s system administrator can update your role, granting you access to the information you need.'
    }
  ];

  const toggleItem = (id: string) => {
    setOpenItem(openItem === id ? null : id);
  };

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 text-white py-12 md:py-24 relative overflow-hidden">
  {/* Decorative elements */}
  <div className="absolute top-0 left-0 w-full h-full opacity-10">
    <div className="absolute top-20 left-20 w-32 h-32 rounded-full bg-blue-400 blur-xl"></div>
    <div className="absolute bottom-10 right-1/4 w-48 h-48 rounded-full bg-indigo-400 blur-xl"></div>
  </div>

  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
    <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
      <div className="max-w-3xl flex flex-col items-center lg:items-start text-center lg:text-left mb-8 lg:mb-0">
        <div className="inline-flex items-center gap-3 mb-4 px-4 py-2 bg-blue-500/20 rounded-full border border-blue-400/30">
          <HelpCircle className="w-5 h-5" />
          <span className="text-sm font-medium tracking-wider">FAQS</span>
        </div>
        
        <h1 className="flex flex-col items-center lg:items-start gap-3 text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-100 to-white">
            Frequently Asked Questions
          </span>
          <span className="flex items-center gap-2 text-blue-200 text-sm font-normal">
            <Sparkles className="w-4 h-4" />
            Everything you need to know
            <Sparkles className="w-4 h-4" />
          </span>
        </h1>
        
        <p className="text-lg md:text-xl text-blue-100/90 mb-6">
          Find answers to common questions about International Responders Systems GrantReady™
        </p>
        
        <div className="flex gap-3">
          <Link to={'/support'}>
          <button className="flex items-center gap-2 px-5 py-3 bg-white text-blue-700 rounded-lg font-medium hover:bg-blue-50 transition-all">
            <MessageCircleQuestion className="w-5 h-5" />
            Ask a question
          </button>
          </Link>
        </div>
      </div>
      
      <div className="w-full sm:w-1/2 flex justify-center lg:justify-end relative">
        <div className="relative">
          <img 
            className="w-48 sm:w-80 rounded-xl shadow-2xl border-4 border-white/10 hover:scale-[1.02] transition-transform" 
            src={FAQIMG} 
            alt="Faq image" 
          />
          <div className="absolute -bottom-4 -right-4 bg-white text-blue-700 p-3 rounded-lg shadow-lg border border-blue-200">
            <MessageSquareWarning className="w-6 h-6" />
          </div>
          <div className="absolute -top-4 -left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg border border-blue-300/30">
            <Lightbulb className="w-6 h-6" />
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-4">
            {faqItems.map((item) => (
              <div
                key={item.id}
                className="bg-white rounded-lg shadow-md overflow-hidden"
              >
                <button
                  onClick={() => toggleItem(item.id)}
                  className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <item.icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex items-baseline space-x-4">
                      <span className="text-sm font-medium text-blue-600">{item.id}</span>
                      <span className="text-lg font-medium text-gray-900">{item.question}</span>
                    </div>
                  </div>
                  <ChevronDown
                    className={`h-5 w-5 text-gray-500 transition-transform ${openItem === item.id ? 'transform rotate-180' : ''
                      }`}
                  />
                </button>
                {openItem === item.id && (
                  <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                    <p className="text-gray-600">{item.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Still Need Help Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Still Need Help?</h2>
          <p className="text-gray-600 mb-8">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
          >
            Contact Support
          </Link>
        </div>
      </section>
    </div>
  );
}