import React, { useState, useEffect } from 'react';
import { Linkedin, Mail } from 'lucide-react';
import { supabase } from '../lib/supabase';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

// Bio component with see more/less functionality
function BioContent({ bio }) {
  const [expanded, setExpanded] = useState(false);
  const maxLength = 150; // Character limit before showing "See More"
  const isLong = bio && bio.length > maxLength;

  if (!bio) return null;

  return (
    <div className="text-gray-600">
      <div className={expanded ? '' : 'line-clamp-3'}>
        <ReactMarkdown 
          remarkPlugins={[remarkGfm]} 
          rehypePlugins={[rehypeRaw]} 
          components={{ 
            h1: ({ children }) => <h1 className="text-3xl font-bold mt-10 mb-6">{children}</h1>, 
            h2: ({ children }) => ( 
              <h2 className="text-2xl font-bold mt-8 mb-4 border-b pb-2">{children}</h2> 
            ), 
            h3: ({ children }) => <h3 className="text-xl font-semibold mt-6 mb-3">{children}</h3>, 
            p: ({ children }) => <p className="mb-6 text-gray-700 leading-relaxed">{children}</p>, 
            ul: ({ children }) => <ul className="list-disc pl-8 mb-6 space-y-2">{children}</ul>, 
            ol: ({ children }) => <ol className="list-decimal pl-8 mb-6 space-y-2">{children}</ol>, 
            li: ({ children }) => <li className="mb-2">{children}</li>, 
            blockquote: ({ children }) => ( 
              <blockquote className="border-l-4 border-blue-500 pl-4 italic bg-gray-50 py-2 mb-6 text-gray-600"> 
                {children} 
              </blockquote> 
            ), 
            a: ({ children, href }) => ( 
              <a href={href} className="text-blue-600 hover:text-blue-800 underline"> 
                {children} 
              </a> 
            ), 
            img: ({ src, alt }) => ( 
              <img src={src} alt={alt} className="my-6 rounded-lg shadow-md max-w-full h-auto" /> 
            ), 
            table: ({ children }) => ( 
              <table className="w-full mb-6 border-collapse">{children}</table> 
            ), 
            th: ({ children }) => ( 
              <th className="py-2 px-4 border bg-gray-100 font-semibold text-left">{children}</th> 
            ), 
            td: ({ children }) => <td className="py-2 px-4 border">{children}</td>, 
            code: ({ inline, className, children }) => { 
              const match = /language-(\w+)/.exec(className || ''); 
              return !inline ? ( 
                <SyntaxHighlighter 
                  style={atomDark} 
                  language={match?.[1] || 'javascript'} 
                  className="rounded-lg mb-6 text-sm" 
                  showLineNumbers 
                  wrapLines 
                > 
                  {String(children).replace(/\n$/, '')} 
                </SyntaxHighlighter> 
              ) : ( 
                <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono"> 
                  {children} 
                </code> 
              ); 
            }, 
            u: ({ children }) => <u className="underline">{children}</u>, 
          }}
        >
          {bio.replace(/\+\+([^\+]+)\+\+/g, '<u>$1</u>')}
        </ReactMarkdown>
      </div>
      {isLong && (
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-blue-600 text-sm mt-2 hover:underline"
        >
          {expanded ? 'See Less' : 'See More'}
        </button>
      )}
    </div>
  );
}

export default function Leadership() {
  const [teamMembers, setTeamMembers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const { data, error } = await supabase
          .from('teams')
          .select('*')
          .order('created_at', { ascending: true });

        if (error) throw error;
        setTeamMembers(data);
      } catch (err) {
        console.error('Error fetching team members:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  if (loading) {
    return (
      <div className="pt-16 flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-6">Leadership Team</h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Meet the visionaries driving innovation in healthcare emergency response
            </p>
          </div>
        </div>
      </section>

      {/* Team Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member) => (
              <div key={member.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <img
                  src={member.image_url}
                  alt={member.name}
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2">{member.name}</h3>
                  <p className="text-blue-600 font-medium mb-4">{member.role}</p>
                  <div className="mb-6">
                    <BioContent bio={member.bio} />
                  </div>
                  <div className="flex space-x-4">
                    {member.linkedin_url && (
                      <a 
                        href={member.linkedin_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="p-2 text-gray-600 hover:text-blue-600"
                      >
                        <Linkedin className="h-5 w-5" />
                      </a>
                    )}
                    {member.email && (
                      <a 
                        href={`mailto:${member.email}`}
                        className="p-2 text-gray-600 hover:text-blue-600"
                      >
                        <Mail className="h-5 w-5" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}