import React from 'react';

// Arrow Right Icon Component
const ArrowRightIcon: React.FC<{ color?: string }> = ({ color = '#000000' }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6 12L10 8L6 4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const LeadershipAndPlanning: React.FC = () => {
  const [isMobile, setIsMobile] = React.useState(false);
  const hereColor = '#A892F7'; // Lavender color for "here" and right arrow
  const containerBgColor = '#F8F9FA'; // Very light gray for page background effect
  const textDarkGray = '#3C4043'; // A common dark gray for text
  const textBlack = '#000000';

  // Track screen size for responsive design
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <>
    <div style={{
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
      backgroundColor: containerBgColor,
      padding: isMobile ? '80px 20px 40px 20px' : '80px 20px 120px 20px', // Less bottom padding on mobile
      textAlign: 'center',
      position: 'relative',
      overflowX: 'hidden', // Prevent horizontal scroll from wide arrows if they extend
    }}>
      {/* Content Wrapper for centering and max-width */}
      <div style={{
        maxWidth: '960px',
        margin: '0 auto',
        position: 'relative',
        zIndex: 1,
      }}>
        {/* Main Heading */}
        <h2 style={{
          fontSize: 'clamp(2rem, 4vw, 2.5rem)',
          fontWeight: 700,
          color: textBlack,
          margin: '0 0 24px 0',
          lineHeight: 1.2,
        }}>
          Leadership and Planning
        </h2>

        {/* Description Text */}
        <p style={{
          fontSize: 'clamp(1rem, 2.5vw, 1.125rem)',
          color: textDarkGray,
          fontWeight: 400,
          maxWidth: '600px',
          margin: '0 auto 48px auto',
          lineHeight: 1.6,
        }}>
          At International Responder Systems, we align our client's objectives with our organizational principles to uphold quality, consistency, integrity, and trust. We prioritize leadership through authentic service.
        </p>

        {/* Buttons Container */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '20px',
          flexWrap: 'wrap',
        }}>
          {/* Our Portfolio Button */}
          <button style={{
            backgroundColor: textBlack,
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '16px 24px',
            fontSize: '1rem',
            fontWeight: 600,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            transition: 'all 0.3s ease',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#333333';
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = textBlack;
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
          }}>
            Our Portfolio
            <span style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <ArrowRightIcon color="white" />
            </span>
          </button>

          {/* See A Demo Button */}
          <button style={{
            backgroundColor: 'white',
            color: textBlack,
            border: `2px solid ${textBlack}`,
            borderRadius: '8px',
            padding: '14px 22px', // Slightly less padding to account for border
            fontSize: '1rem',
            fontWeight: 600,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            transition: 'all 0.3s ease',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = textBlack;
            e.currentTarget.style.color = 'white';
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'white';
            e.currentTarget.style.color = textBlack;
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
          }}>
            See A Demo
            <span style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
              <ArrowRightIcon color={textBlack} />
            </span>
          </button>
        </div>
      </div>

    </div>

    {/* Bottom Image - Full Width - Outside main container */}
    <div style={{
      width: '100%',
      height: '400px',
      backgroundImage: 'url("/path-to-your-leadership-image.jpg")', // Replace with actual image path
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
    }}>
      {/* Optional overlay for better text readability if needed */}
      <div style={{
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(0, 0, 0, 0.1)', // Light overlay
      }} />
    </div>
    </>
  );
};

export default LeadershipAndPlanning;
