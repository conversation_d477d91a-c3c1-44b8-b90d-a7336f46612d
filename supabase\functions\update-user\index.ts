import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.21.0';

// Set up CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { ...corsHeaders }
      });
    }

    // Get the request body
    const { 
      userId, 
      email, 
      password, 
      full_name, 
      role 
    } = await req.json();

    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: { ...corsHeaders }
      });
    }

    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get the user making the request
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...corsHeaders }
      });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders }
      });
    }

    // Check if the user is an admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || !profile || profile.role !== 'admin') {
      return new Response(JSON.stringify({ error: 'Unauthorized - Admin privileges required' }), {
        status: 403,
        headers: { ...corsHeaders }
      });
    }

    // Check if the user exists
    const { data: userToUpdate, error: getUserError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (getUserError || !userToUpdate) {
      return new Response(JSON.stringify({ error: 'User not found' }), {
        status: 404,
        headers: { ...corsHeaders }
      });
    }

    // Prepare updates
    const updates: Record<string, any> = {};
    let authUpdates = false;

    // Update auth user if email or password is provided
    if (email || password) {
      const authUpdateData: Record<string, any> = {};
      
      if (email && email !== userToUpdate.email) {
        authUpdateData.email = email;
      }
      
      if (password) {
        authUpdateData.password = password;
      }
      
      if (Object.keys(authUpdateData).length > 0) {
        const { error: updateAuthError } = await supabaseAdmin.auth.admin.updateUserById(
          userId,
          authUpdateData
        );

        if (updateAuthError) {
          return new Response(JSON.stringify({ error: updateAuthError.message }), {
            status: 400,
            headers: { ...corsHeaders }
          });
        }
        
        authUpdates = true;
      }
    }

    // Update profile data
    if (full_name !== undefined) {
      updates.full_name = full_name;
    }
    
    if (role !== undefined && (role === 'admin' || role === 'user')) {
      updates.role = role;
    }
    
    if (email !== undefined && email !== userToUpdate.email) {
      updates.email = email;
    }

    // Only update profile if there are changes
    if (Object.keys(updates).length > 0) {
      const { error: updateProfileError } = await supabaseAdmin
        .from('profiles')
        .update(updates)
        .eq('id', userId);

      if (updateProfileError) {
        return new Response(JSON.stringify({ error: updateProfileError.message }), {
          status: 400,
          headers: { ...corsHeaders }
        });
      }
    }

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'User updated successfully',
      authUpdated: authUpdates,
      profileUpdated: Object.keys(updates).length > 0
    }), {
      status: 200,
      headers: { ...corsHeaders }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders }
    });
  }
});
