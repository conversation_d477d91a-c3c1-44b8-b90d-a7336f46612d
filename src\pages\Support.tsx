import React, { useState, useEffect } from 'react';
import { MessageSquare, Search, Filter, Clock, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../store/authStore';
import { Link } from 'react-router-dom';

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  user_id: string;
  contact_name: string;
  contact_phone: string;
  resolution_comment?: string;
  resolved_by?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  user: {
    email: string;
    full_name: string;
    phone: string;
  };
}

export default function Support() {
  const user = useAuthStore((state) => state.user);
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'technical',
    priority: 'medium' as const,
    contact_name: '',
    contact_phone: ''
  });

  useEffect(() => {
    if (user) {
      fetchUserProfile();
      fetchTickets();
    }
  }, [user]);

  const fetchUserProfile = async () => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('full_name, phone')
        .eq('id', user?.id)
        .single();

      if (error) throw error;

      if (profile) {
        setFormData(prev => ({
          ...prev,
          contact_name: profile.full_name || '',
          contact_phone: profile.phone || ''
        }));
      }
    } catch (err) {
      console.error('Error fetching user profile:', err);
    }
  };

  const fetchTickets = async () => {
    try {
      const { data, error } = await supabase
        .from('support_tickets')
        .select(`
          *,
          user:profiles!support_tickets_user_id_fkey(
            email,
            full_name,
            phone
          )
        `)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTickets(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.contact_name.trim()) {
      setError('Please provide your name');
      return;
    }

    if (!formData.contact_phone.trim()) {
      setError('Please provide your phone number');
      return;
    }

    try {
      const { error } = await supabase
        .from('support_tickets')
        .insert([{
          ...formData,
          user_id: user?.id,
          status: 'open'
        }]);

      if (error) throw error;

      // Prepare email data
      const emailData = {
        name: formData.contact_name,
        email: user?.email,
        phone: formData.contact_phone,
        company: '', // Add company if available in your formData
        message: `
          <strong>Title:</strong> ${formData.title}<br/>
          <strong>Description:</strong> ${formData.description}<br/>
          <strong>Category:</strong> ${formData.category}<br/>
          <strong>Priority:</strong> ${formData.priority}
        `
      };

      // Send email to admin
      await supabase.functions.invoke('send-contact-email', {
        body: {
          to: '<EMAIL>',
          subject: `New Support Ticket from ${emailData.name}`,
          html: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #2c3e50; max-width: 600px; margin: auto;">
  <h2 style="color: #2c3e50; border-bottom: 2px solid #ccc; padding-bottom: 5px;">New Support Ticket - IRS Website</h2>

  <table cellpadding="10" cellspacing="0" border="0" style="width: 100%; border-collapse: collapse;">
    <tbody>
      <tr>
        <td style="font-weight: bold; width: 120px;">Name:</td>
        <td>${emailData.name}</td>
      </tr>
      <tr>
        <td style="font-weight: bold;">Email:</td>
        <td><a href="mailto:${emailData.email}" style="color: #3498db;">${emailData.email}</a></td>
      </tr>
      ${emailData.phone ? `
      <tr>
        <td style="font-weight: bold;">Phone:</td>
        <td><a href="tel:${emailData.phone}" style="color: #3498db;">${emailData.phone}</a></td>
      </tr>` : ''}
      ${emailData.company ? `
      <tr>
        <td style="font-weight: bold;">Company:</td>
        <td>${emailData.company}</td>
      </tr>` : ''}
    </tbody>
  </table>

  <p style="margin-top: 20px; font-weight: bold;">Message:</p>
  <div style="background-color: #f4f6f8; padding: 15px; border-left: 4px solid #2980b9; white-space: pre-wrap;">
    ${emailData.message}
  </div>
</div>

          `
        }
      });

      // Send confirmation email to user
      await supabase.functions.invoke('send-contact-email', {
        body: {
          to: emailData.email,
          subject: 'Your Support Ticket has been received',
          html: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
              <h2 style="color: #2c3e50;">Thank you for contacting IRS Support</h2>
              <p>Dear ${emailData.name},</p>
              <p>We have received your support ticket and our team will get back to you as soon as possible.</p>
              <p><strong>Ticket Details:</strong></p>
              <ul>
                <li><strong>Title:</strong> ${formData.title}</li>
                <li><strong>Description:</strong> ${formData.description}</li>
                <li><strong>Category:</strong> ${formData.category}</li>
                <li><strong>Priority:</strong> ${formData.priority}</li>
              </ul>
              <p>Thank you,<br/>IRS Support Team</p>
            </div>
          `
        }
      });

      setFormData({
        title: '',
        description: '',
        category: 'technical',
        priority: 'medium',
        contact_name: formData.contact_name,
        contact_phone: formData.contact_phone
      });
      setShowForm(false);
      fetchTickets();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'resolved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'closed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen pt-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-12 text-center">
          <h2 className="text-2xl font-bold mb-4">Please Sign In</h2>
          <p className="text-gray-600 mb-8">You need to be signed in to access support.</p>
          <Link
            to="/login"
            className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold mb-6">Support Center</h1>
            <p className="text-xl text-blue-100 mb-8">
              Get help with your questions and issues. Our support team is here to assist you.
            </p>
            <div className="flex space-x-4">
              <button
                onClick={() => setShowForm(true)}
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                Create New Ticket
              </button>
              <a
                href="mailto:<EMAIL>"
                className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
              >
                Email Support
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {error && (
            <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-8">
              {error}
            </div>
          )}

          {showForm && (
            <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
              <h2 className="text-2xl font-bold mb-6">Create Support Ticket</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Your Name
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.contact_name}
                      onChange={(e) => setFormData({ ...formData, contact_name: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Full Name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      required
                      value={formData.contact_phone}
                      onChange={(e) => setFormData({ ...formData, contact_phone: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Phone Number"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Brief description of your issue"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    required
                    rows={4}
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Detailed description of your issue"
                  />
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="technical">Technical Issue</option>
                      <option value="billing">Billing</option>
                      <option value="account">Account</option>
                      <option value="feature">Feature Request</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priority
                    </label>
                    <select
                      value={formData.priority}
                      onChange={(e) => setFormData({ ...formData, priority: e.target.value as 'low' | 'medium' | 'high' | 'urgent' })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>
                </div>

                <div className="flex justify-end space-x-4">
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="text-gray-600 hover:text-gray-900"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
                  >
                    Submit Ticket
                  </button>
                </div>
              </form>
            </div>
          )}

          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Your Support Tickets</h2>
                {/* <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search tickets..."
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <button className="flex items-center text-gray-600 hover:text-gray-900">
                    <Filter className="h-5 w-5 mr-2" />
                    Filter
                  </button>
                </div> */}
              </div>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : tickets.length > 0 ? (
              <div className="divide-y">
                {tickets.map((ticket) => (
                  <div key={ticket.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        {getStatusIcon(ticket.status)}
                        <div>
                          <h3 className="font-semibold text-lg mb-1">{ticket.title}</h3>
                          <p className="text-gray-600 mb-2">{ticket.description}</p>
                          <div className="flex items-center space-x-4 text-sm">
                            <span className={`px-2 py-1 rounded-full ${getPriorityColor(ticket.priority)}`}>
                              {ticket.priority}
                            </span>
                            <span className="text-gray-500">
                              {new Date(ticket.created_at).toLocaleDateString()}
                            </span>
                            <span className="text-gray-500">
                              Contact: {ticket.contact_name} ({ticket.contact_phone})
                            </span>
                            <span className="text-gray-500">
                              #{ticket.id.split('-')[0]}
                            </span>
                          </div>
                          {ticket.resolution_comment && (
                            <div className="mt-4 bg-gray-50 p-4 rounded-lg">
                              <p className="text-sm font-medium text-gray-700">Resolution:</p>
                              <p className="text-gray-600">{ticket.resolution_comment}</p>
                            </div>
                          )}
                        </div>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${ticket.status === 'resolved'
                          ? 'bg-green-100 text-green-800'
                          : ticket.status === 'closed'
                            ? 'bg-red-100 text-red-800'
                            : ticket.status === 'in_progress'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-blue-100 text-blue-800'
                        }`}>
                        {ticket.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No support tickets</h3>
                <p className="text-gray-600">
                  You haven't created any support tickets yet.
                </p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}