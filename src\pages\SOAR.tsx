import React, { useState, useEffect } from 'react';
import { Brain, ArrowRight, <PERSON><PERSON>hart, Users, Building2, Link as LinkIcon, Activity, Globe, ChevronRight, ShoppingCart } from 'lucide-react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import ProductDisplay from '../components/ProductDisplay';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages?: string[];
  specifications: string[];
  theme_color?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
  updated_at?: string;
}

export default function SOAR() {
  const metabaseDashboardUrl = "https://metabase.chriszhu.me/public/dashboard/************************************?all_options=&location=";
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  const [maxYearlyDiscount, setMaxYearlyDiscount] = useState<number>(0);

  useEffect(() => {
    fetchProducts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchProducts = async () => {
    try {
      console.log('Fetching products for the SOAR page');

      // First, let's get all products to examine their structure
      const { data: allProducts, error: fetchError } = await supabase
        .from('products')
        .select('*');

      if (fetchError) {
        console.error('Error fetching products:', fetchError);
        setProducts([]);
        setError(`Error fetching products: ${fetchError.message || fetchError}`);
        setLoading(false);
        return;
      }

      console.log('All products from database:', allProducts);

      // Filter products client-side for SOAR page
      const soarProducts = allProducts.filter(product => {
        // Check if pages exists and is an array
        if (Array.isArray(product.pages)) {
          return product.pages.includes('SOAR');
        }
        // If pages is a string (JSON string), try to parse it
        if (typeof product.pages === 'string') {
          try {
            const pagesArray = JSON.parse(product.pages);
            return Array.isArray(pagesArray) && pagesArray.includes('SOAR');
          } catch (e) {
            console.error('Error parsing pages JSON:', e);
            return false;
          }
        }
        return false;
      });

      console.log('Filtered SOAR products:', soarProducts);

      // If we have products for SOAR page
      if (soarProducts && soarProducts.length > 0) {
        // Sort by price to ensure consistent order
        const sortedProducts = [...soarProducts].sort((a, b) => (a.price || 0) - (b.price || 0));

        // Log the sorted products with formatted prices for debugging
        console.log('Sorted SOAR products:', sortedProducts.map(p => ({
          id: p.id,
          name: p.name,
          price: p.price.toFixed(2),
          monthly_discount: p.monthly_discount || 0,
          yearly_discount: p.yearly_discount || 0
        })));

        // Find the maximum yearly discount among all products
        const highestDiscount = sortedProducts.reduce((max, product) => {
          const discount = product.yearly_discount || 0;
          return discount > max ? discount : max;
        }, 0);

        setMaxYearlyDiscount(highestDiscount);
        setProducts(sortedProducts);
      } else {
        // If there are no products for SOAR page, set empty array
        console.log('No products found for SOAR page');
        setProducts([]);
      }
    } catch (err: unknown) {
      console.error('Error fetching products:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-orange-500 via-orange-600 to-orange-700 text-white py-12 sm:py-14 lg:py-16 overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated gradient orbs */}
          <div className="absolute -top-1/2 -left-1/2 w-full h-full rounded-full bg-gradient-to-br from-orange-300/30 to-transparent blur-3xl animate-[spin_30s_linear_infinite]"></div>
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full rounded-full bg-gradient-to-br from-orange-400/30 to-transparent blur-3xl animate-[spin_25s_linear_infinite_reverse]"></div>

          {/* Animated grid pattern */}
          <div
            className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiIGlkPSJhIj48c3RvcCBzdG9wLWNvbG9yPSIjZmZmIiBzdG9wLW9wYWNpdHk9Ii4yIiBvZmZzZXQ9IjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iI2ZmZiIgc3RvcC1vcGFjaXR5PSIwIiBvZmZzZXQ9IjEwMCUiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cGF0aCBkPSJNMCAwaDYwdjYwSDB6IiBmaWxsPSJ1cmwoI2EpIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=')]
            opacity-20
            animate-[gradient_15s_ease_infinite]"
          ></div>

          {/* Subtle overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/80 via-orange-600/80 to-orange-700/80 backdrop-blur-[2px]"></div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-6 lg:gap-8 items-center relative">
            <div className="relative z-10 animate-[fadeIn_1s_ease-out]">
              <h1 className="text-3xl lg:text-4xl font-bold mb-3 [text-shadow:_0_1px_2px_rgb(0_0_0_/_20%)]">
                Predict Tomorrow
                Act Today
              </h1>
              <p className="text-base lg:text-lg mb-3 text-orange-100">
              We are working to save lives and stop the spread of outbreaks using the latest forecasting models
              </p>
              <p className="text-base lg:text-lg mb-3 text-orange-100">
              Learn more about our partnership with the CDC
              </p>
              <div className="space-x-3">
                <a
                  href="https://www.cdc.gov/media/releases/2023/p0922-disease-modeling.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-white text-orange-600 px-5 py-2 rounded-lg font-semibold hover:bg-orange-50 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 inline-block"
                >
                  Learn More
                </a>
                {/* <a
                  href={metabaseDashboardUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white/10 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 inline-block"
                >
                  Watch Demo
                </a> */}
                <Link
                  to="/pacific-rim"
                  className="border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white/10 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 inline-block"
                >
                  Pacific Rim
                </Link>
              </div>
            </div>

            {/* Epidemic Forecasting Visualization */}
            <div className="relative z-10 animate-[slideIn_1s_ease-out]">
              <div className="bg-white/10 backdrop-blur-md p-2 sm:p-3 rounded-xl transform hover:scale-[1.02] transition-all duration-300 hover:shadow-2xl">
                <div className="relative aspect-[16/9] rounded-lg overflow-hidden">
                  {/* Main visualization image */}
                  <img
                    src="https://images.unsplash.com/photo-1584036561566-baf8f5f1b144?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                    alt="Epidemic Forecasting Dashboard"
                    className="w-full h-full object-cover"
                  />

                  {/* Animated overlay elements */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent">
                    {/* Animated data points */}
                    <div className="absolute bottom-3 left-3 right-3">
                      <div className="grid grid-cols-3 gap-2">
                        <div className="bg-white/20 backdrop-blur-md p-2 rounded-lg animate-[fadeIn_0.5s_ease-out_0.2s] opacity-0 [animation-fill-mode:forwards]">
                          <div className="text-xs font-medium">Prediction</div>
                          <div className="text-lg font-bold">94.8%</div>
                        </div>
                        <div className="bg-white/20 backdrop-blur-md p-2 rounded-lg animate-[fadeIn_0.5s_ease-out_0.4s] opacity-0 [animation-fill-mode:forwards]">
                          <div className="text-xs font-medium">Data Points</div>
                          <div className="text-lg font-bold">1.2M+</div>
                        </div>
                        <div className="bg-white/20 backdrop-blur-md p-2 rounded-lg animate-[fadeIn_0.5s_ease-out_0.6s] opacity-0 [animation-fill-mode:forwards]">
                          <div className="text-xs font-medium">Response</div>
                          <div className="text-lg font-bold">Real-time</div>
                        </div>
                      </div>
                    </div>

                    {/* Animated indicators */}
                    <div className="absolute top-3 right-3 flex items-center space-x-2">
                      <div className="flex items-center bg-green-500/20 backdrop-blur-md px-2 py-0.5 rounded-full">
                        <div className="w-1.5 h-1.5 rounded-full bg-green-500 animate-pulse mr-1.5"></div>
                        <span className="text-xs font-medium">Live Updates</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Interactive elements */}
              <div className="mt-1.5 flex justify-end space-x-3">
                <a
                  href={metabaseDashboardUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-xs text-white/80 hover:text-white transition-colors"
                >
                  <Activity className="h-3 w-3 mr-1" />
                  View Live Data
                </a>
                {/* <a
                  href={metabaseDashboardUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-xs text-white/80 hover:text-white transition-colors"
                >
                  <Globe className="h-3 w-3 mr-1" />
                  Global View
                </a> */}
                <button className="flex items-center text-xs text-white/80 hover:text-white transition-colors">
                  <ChevronRight className="h-3 w-3" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative bottom curve */}
        <div
          className="absolute bottom-0 left-0 right-0 h-12"
          style={{
            clipPath: 'polygon(0 100%, 100% 100%, 100% 0)',
            transform: 'scale(1.1)',
            background: 'white'
          }}
        ></div>
      </section>

      {/* Partners Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Cooperative Partnerships</h2>
              <p className="text-gray-600 mb-8">
                Building a platform to nowcast and forecast outcomes is not a trivial task, it comes with a host of barriers to clear and challenges to solve. We are partnering with various stakeholders to ensure we hit the mark. This will be a network of networks learning and sharing information.
              </p>
              <div className="space-y-6">
                <a
                  href="https://www.cdc.gov/media/releases/2023/p0922-disease-modeling.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-start">
                    <LinkIcon className="h-6 w-6 text-orange-600 mt-1" />
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold mb-2">CDC Partnership</h3>
                      <p className="text-gray-600">Learn more about our collaboration with the CDC in disease modeling and prediction.</p>
                    </div>
                  </div>
                </a>

                <a
                  href="https://www.internationalrespondersystems.com/copy-of-pacific-rim-forcasting"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-start">
                    <LinkIcon className="h-6 w-6 text-orange-600 mt-1" />
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold mb-2">Pacific Rim Forecasting</h3>
                      <p className="text-gray-600">Explore our Pacific Rim forecasting initiatives and partnerships.</p>
                    </div>
                  </div>
                </a>
              </div>
            </div>
            <div>
              <img
                src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                alt="Partnership visualization"
                className="rounded-lg shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Solutions</h2>
            <p className="text-xl text-gray-600 mb-8">Choose the perfect plan for your organization</p>

            <div className="bg-white/10 backdrop-blur-sm p-2 rounded-lg inline-flex mb-8 shadow-sm">
              <button
                onClick={() => setBillingInterval('monthly')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'monthly'
                  ? 'bg-orange-600 text-white'
                  : 'text-gray-700 hover:bg-gray-100'
                  }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingInterval('yearly')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'yearly'
                  ? 'bg-orange-600 text-white'
                  : 'text-gray-700 hover:bg-gray-100'
                  }`}
              >
                Yearly
                {maxYearlyDiscount > 0 && (
                  <span className="ml-2 text-sm bg-green-500 text-white px-2 py-1 rounded-full">
                    Save up to {maxYearlyDiscount}%
                  </span>
                )}
              </button>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
            </div>
          ) : (
            <ProductDisplay products={products} pageName="SOAR" billingInterval={billingInterval} />
          )}
        </div>
      </section>

      {/* Core Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Approach</h2>
            <p className="text-xl text-gray-600">Innovate • Integrate • Implement</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <BarChart className="h-12 w-12 text-orange-600 mb-4" />
              <h3 className="text-xl font-bold mb-4">Data Modeling and Analytics</h3>
              <p className="text-gray-600 mb-4">
                Ingesting data, checking its integrity, training and testing models. We are fine tuning models to push the limit on our prediction accuracy to best prepare for any outcome.
              </p>
              <a
                href="https://www.cdc.gov/forecast-outbreak-analytics/partners/insightnet/implementers.html"
                target="_blank"
                rel="noopener noreferrer"
                className="text-orange-600 font-medium hover:text-orange-700 inline-flex items-center"
              >
                Learn More
                <ArrowRight className="ml-2 h-5 w-5" />
              </a>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg">
              <Building2 className="h-12 w-12 text-orange-600 mb-4" />
              <h3 className="text-xl font-bold mb-4">Pacific Rim Partners</h3>
              <p className="text-gray-600 mb-4">
                Collaborating with key stakeholders across the Pacific Rim to enhance our forecasting capabilities and response strategies.
              </p>
              <Link
                to="/pacific-rim"
                className="text-orange-600 font-medium hover:text-orange-700 inline-flex items-center"
              >
                Learn More
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg">
              <Users className="h-12 w-12 text-orange-600 mb-4" />
              <h3 className="text-xl font-bold mb-4">Directing Public Health Workforce</h3>
              <p className="text-gray-600 mb-4">
                Designing and implementing a continuous education program to upskill our current public health workforce and increasing the pipeline of trained workers.
              </p>
              <button className="text-orange-600 font-medium hover:text-orange-700 inline-flex items-center">
                Learn More
                <ArrowRight className="ml-2 h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-orange-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 class="text-3xl font-bold mb-4">Ready to get started?</h2>
          <p class="text-xl mb-8">Find out more about how we can serve you and your customers</p>
          <Link to="/contact" class="inline-flex items-center bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-orange-50 transition duration-300">
            Contact Us
            <ArrowRight class="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>
    </div>
  );
}