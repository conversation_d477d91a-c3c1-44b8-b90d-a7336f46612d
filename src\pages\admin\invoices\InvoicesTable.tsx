import React from 'react';
import { format } from 'date-fns';
import { ChevronLeft, ChevronRight } from 'lucide-react';

// Assuming the Invoice interface is defined here or imported
interface Invoice {
    id: string;
    stripe_invoice_id: string;
    stripe_customer_id: string | null;
    customer_email: string | null;
    customer_name: string | null;
    status: string | null;
    amount_due: number | null;
    amount_paid: number | null;
    amount_remaining: number | null;
    currency: string | null;
    due_date: string | null;
    invoice_pdf: string | null;
    hosted_invoice_url: string | null;
    created_at: string;
}

interface InvoicesTableProps {
    invoices: Invoice[];
    itemsPerPage: number;
    onItemsPerPageChange: (size: number) => void;
    currentPageIndex: number; // New prop
    hasNextPage: boolean; // New prop
    onNextPage: () => void; // New prop
    onPreviousPage: () => void; // New prop
}

const InvoicesTable: React.FC<InvoicesTableProps> = ({
    invoices,
    itemsPerPage,
    onItemsPerPageChange,
    currentPageIndex,
    hasNextPage,
    onNextPage,
    onPreviousPage,
}) => {

    // --- Calculate display range based on current page index and items per page ---
    const firstItemIndex = currentPageIndex * itemsPerPage + 1;
    const lastItemIndex = currentPageIndex * itemsPerPage + invoices.length;
    // --- End Calculation ---


    const formatCurrency = (amount: number | null, currency: string | null) => {
        if (amount === null || currency === null) return 'N/A';
        // Stripe amounts are usually in cents
        return new Intl.NumberFormat('en-US', { style: 'currency', currency: currency.toUpperCase() }).format(amount / 100);
    };

    const formatDate = (dateString: string | null) => {
        if (!dateString) return 'N/A';
        try {
            return format(new Date(dateString), 'PPp'); // e.g., Sep 21, 2023, 10:30:00 AM
        } catch (e) {
            return 'Invalid Date';
        }
    };

    return (
        <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
                {/* ... Table Head ... */}
                <thead className="bg-gray-50">
                    <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount Due</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                    {invoices.length === 0 ? (
                        <tr>
                            <td colSpan={6} className="px-6 py-4 text-center text-gray-500">No invoices found.</td>
                        </tr>
                    ) : (
                        invoices.map((invoice) => (
                            <tr key={invoice.id}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {/* Display customer email or name */}
                                    {invoice.customer_name || invoice.customer_email || 'N/A'}
                                    {invoice.stripe_customer_id && <div className="text-xs text-gray-500">ID: {invoice.stripe_customer_id}</div>}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">
                                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                                        invoice.status === 'open' ? 'bg-yellow-100 text-yellow-800' :
                                            invoice.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                                                invoice.status === 'void' ? 'bg-red-100 text-red-800' :
                                                    invoice.status === 'uncollectible' ? 'bg-orange-100 text-orange-800' :
                                                        'bg-gray-100 text-gray-800' // Default/fallback
                                        }`}>
                                        {invoice.status ? invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1) : 'N/A'}
                                    </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {formatCurrency(invoice.amount_due, invoice.currency)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {formatDate(invoice.due_date)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {formatDate(invoice.created_at)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    {invoice.hosted_invoice_url && (
                                        <a href={invoice.hosted_invoice_url} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-900">View</a>
                                    )}
                                    {invoice.invoice_pdf && (
                                        <a href={invoice.invoice_pdf} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-900">PDF</a>
                                    )}
                                    {/* Add other actions like 'Resend', 'Void' etc. if needed, potentially calling other functions */}
                                </td>
                            </tr>
                        ))
                    )}
                </tbody>
            </table>
            {/* --- Updated Pagination Controls --- */}
            <div className="py-3 px-6 flex items-center justify-between border-t border-gray-200">
                <div className="flex-1 flex justify-between sm:hidden">
                    <button
                        onClick={onPreviousPage}
                        disabled={currentPageIndex === 0}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Previous
                    </button>
                    <button
                        onClick={onNextPage}
                        disabled={!hasNextPage}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Next
                    </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <label htmlFor="itemsPerPage" className="text-sm font-medium text-gray-700 mr-2">Items per page:</label>
                        <select
                            id="itemsPerPage"
                            value={itemsPerPage}
                            onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                        >
                            <option>10</option>
                            <option>25</option>
                            <option>50</option>
                            <option>100</option>
                        </select>
                    </div>
                    <div>
                        {/* --- Updated Results Display --- */}
                        <p className="text-sm text-gray-700">
                            Showing <span className="font-medium">{invoices.length > 0 ? firstItemIndex : 0}</span> to <span className="font-medium">{lastItemIndex}</span> results
                            {/* Removed total results count as it's not available with cursor pagination */}
                        </p>
                        {/* --- End Updated Results Display --- */}
                    </div>
                    <div>
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button
                                onClick={onPreviousPage}
                                disabled={currentPageIndex === 0}
                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <span className="sr-only">Previous</span>
                                <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                            </button>
                            <button
                                onClick={onNextPage}
                                disabled={!hasNextPage}
                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <span className="sr-only">Next</span>
                                <ChevronRight className="h-5 w-5" aria-hidden="true" />
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
            {/* --- End Updated Pagination Controls --- */}
        </div>
    );
};

export default InvoicesTable;
