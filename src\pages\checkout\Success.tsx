import { CheckCircle2 } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { useEffect, useState } from 'react';
import { useCartStore } from '../../store/cartStore';
import { supabase } from '../../lib/supabase';

export default function CheckoutSuccess() {
  const navigate = useNavigate();
  const user = useAuthStore((state) => state.user);
  const [subscriptionRefs, setSubscriptionRefs] = useState<string[]>([]);
  const { clearCart } = useCartStore();

  useEffect(() => {
    const fetchRecentSubscriptions = async () => {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();

      const { data } = await supabase
        .from('subscriptions')
        .select('ref')
        .gte('created_at', fiveMinutesAgo)
        .order('created_at', { ascending: false });
      
      // Remove the misplaced JSX code here
      if (data) {
        if (data.length === 0) {
          navigate('/profile');
        } else {
          setSubscriptionRefs(data.map(d => d.ref));
        }
      }
    };

    fetchRecentSubscriptions();
  }, [navigate]); // Add navigate to dependency array
  
  // Keep the existing cart clearing useEffect
  useEffect(() => {
    if (subscriptionRefs.length > 0) {
      clearCart();
    }
  }, [subscriptionRefs, clearCart]);

  return (
    <div className="min-h-screen pt-16 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-20">
        <div className="text-center">
          <CheckCircle2 className="h-16 w-16 text-green-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Payment Successful!
          </h2>
          <p className="text-gray-600 mb-8">
            Thank you for your subscription, {user?.email}.<br />
            Your payment has been processed successfully.
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Return Home
            </Link>
            <button
              onClick={() => navigate('/profile')}
              className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
            >
              View Subscriptions
            </button>
          </div>

          <div className="mt-8 text-sm text-gray-500">
            <p>Need help? <a href="/contact" className="text-blue-600 hover:underline">Contact support</a></p>
            {subscriptionRefs.length > 0 && (
              <div className="mt-2">
                <p>Subscription References:</p>
                {subscriptionRefs.map(ref => (
                  <p key={ref}>#{ref}</p>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}