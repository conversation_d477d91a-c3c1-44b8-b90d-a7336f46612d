import React from 'react';
import { Shield, Mail, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const sections = [
  {
    title: "1. Introduction",
    content: 'Welcome to International Responder Systems ("we," "our," or "us"). This Privacy Policy outlines how we collect, use, and store your personal information when you interact with our services.'
  },
  {
    title: "2. Information We Collect",
    content: "We collect the following types of personal information:\n\n- Name\n- Phone number\n- Email\n- Company name"
  },
  {
    title: "3. Methods of Data Collection",
    content: "We collect data through user input when you provide information voluntarily."
  },
  {
    title: "4. Use of Collected Data",
    content: "We use the collected data for the following purposes:\n\n- Services support\n- Information requests"
  },
  {
    title: "5. Selling of Data",
    content: "We do not sell your personal data to third parties."
  },
  {
    title: "6. Data Storage",
    content: "We store the collected data securely using the wix platform."
  },
  {
    title: "7. Data Storage Duration",
    content: "The data is disposed of as soon as the request is handled. We do not retain your information longer than necessary for the specified purposes."
  },
  {
    title: "8. Information Security",
    content: "We implement appropriate security measures to protect your data from unauthorized access, disclosure, alteration, and destruction."
  },
  {
    title: "9. Your Rights",
    content: "You have the right to:\n\n- Access your personal data\n- Correct inaccuracies in your personal data\n- Request the deletion of your personal data"
  },
  {
    title: "10. Changes to this Privacy Policy",
    content: "We may update this Privacy Policy from time to time. The latest version will be posted on our website."
  },
  {
    title: "11. Contact Us",
    content: "If you have any questions or concerns regarding this Privacy Policy, please contact us at:\n\nInternational Responder Systems\n157 E. Main Street\nElkton, MD 21921-5977\<EMAIL>"
  }
];

export default function Privacy() {
  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-4 mb-6">
            <Shield className="h-10 w-10" />
            <h1 className="text-4xl font-bold">Privacy Policy</h1>
          </div>
          <p className="text-xl text-blue-100 max-w-3xl">
            Your privacy is important to us. Learn how we collect, use, and protect your information.
          </p>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-xl p-8">
            {sections.map((section, index) => (
              <div key={index} className="mb-12 last:mb-0">
                <h2 className="text-2xl font-bold mb-4">{section.title}</h2>
                <div className="prose prose-blue max-w-none">
                  {section.content.split('\n\n').map((paragraph, i) => (
                    <p key={i} className="text-gray-600 leading-relaxed whitespace-pre-line">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>
            ))}

            {/* Contact Section */}
            <div className="mt-12 bg-gray-50 p-8 rounded-lg">
              <h2 className="text-2xl font-bold mb-4">Questions About Privacy?</h2>
              <p className="text-gray-600 mb-6">
                If you have any questions about our Privacy Policy, please contact us.
              </p>
              <div className="flex items-center space-x-6">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
                >
                  <Mail className="mr-2 h-5 w-5" />
                  Email Us
                </a>
                <Link
                  to="/contact"
                  className="inline-flex items-center text-blue-600 font-medium hover:text-blue-700"
                >
                  Contact Page
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}