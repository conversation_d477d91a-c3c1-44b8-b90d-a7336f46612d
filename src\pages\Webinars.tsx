import { useState, useEffect } from 'react';
import { useScrollPosition } from '../hooks/useScrollPosition';
import { Calendar, Clock, Users, Video, Play, AlertCircle, CheckCircle, X, Search, User, MonitorPlay } from 'lucide-react';
import { supabase } from '../lib/supabase';
import WebinarRegistrationModal from '../components/WebinarRegistrationModal';
import WebinarSuccessModal from '../components/WebinarSuccessModal';
import WebinarCancellationModal from '../components/WebinarCancellationModal';
import { useAuthStore } from '../store/authStore';

// Helper function to convert 24-hour time to 12-hour time with AM/PM
const formatTimeWithAMPM = (time: string): string => {
  if (!time) return '';

  // Parse the time string (expected format: HH:MM)
  const [hours, minutes] = time.split(':').map(Number);

  if (isNaN(hours) || isNaN(minutes)) return time;

  // Convert to 12-hour format
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
};

interface Webinar {
  id: string;
  title: string;
  description: string;
  date: string;
  time?: string;
  duration?: string;
  speaker: string;
  image_url: string;
  recording_url?: string;
  webinar_link?: string;
  status: 'upcoming' | 'recorded' | 'live';
  created_at: string;
  updated_at: string;
  attendees?: number; // For UI display
  views?: number; // For UI display
}

export default function Webinars() {
  const user = useAuthStore((state) => state.user);
  const [upcomingWebinars, setUpcomingWebinars] = useState<Webinar[]>([]);
  const [recordedWebinars, setRecordedWebinars] = useState<Webinar[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [selectedWebinar, setSelectedWebinar] = useState<Webinar | null>(null);
  const [userRegistrations, setUserRegistrations] = useState<{[key: string]: boolean}>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUpcomingWebinars, setFilteredUpcomingWebinars] = useState<Webinar[]>([]);
  const [filteredRecordedWebinars, setFilteredRecordedWebinars] = useState<Webinar[]>([]);
  const [expandedDescriptions, setExpandedDescriptions] = useState<Record<string, boolean>>({});
  const { saveScrollPosition, restoreScrollPosition } = useScrollPosition();

  useEffect(() => {
    fetchWebinars();
  }, []);

  useEffect(() => {
    if (user) {
      fetchUserRegistrations();
    } else {
      setUserRegistrations({});
    }
  }, [user]);

  // Filter webinars based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredUpcomingWebinars(upcomingWebinars);
      setFilteredRecordedWebinars(recordedWebinars);
      return;
    }

    const query = searchQuery.toLowerCase();

    // Filter upcoming webinars
    const filteredUpcoming = upcomingWebinars.filter(webinar =>
      webinar.title.toLowerCase().includes(query) ||
      webinar.description.toLowerCase().includes(query) ||
      webinar.speaker.toLowerCase().includes(query)
    );

    // Filter recorded webinars
    const filteredRecorded = recordedWebinars.filter(webinar =>
      webinar.title.toLowerCase().includes(query) ||
      webinar.description.toLowerCase().includes(query) ||
      webinar.speaker.toLowerCase().includes(query)
    );

    setFilteredUpcomingWebinars(filteredUpcoming);
    setFilteredRecordedWebinars(filteredRecorded);
  }, [searchQuery, upcomingWebinars, recordedWebinars]);

  const fetchUserRegistrations = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('webinar_registrations')
        .select('webinar_id, status')
        .eq('user_id', user.id)
        .neq('status', 'cancelled');

      if (error) throw error;

      if (data) {
        const registrations = data.reduce((acc, reg) => {
          acc[reg.webinar_id] = true;
          return acc;
        }, {} as {[key: string]: boolean});

        setUserRegistrations(registrations);
      }
    } catch (err) {
      console.error('Error fetching user registrations:', err);
    }
  };

  const fetchWebinars = async () => {
    try {
      setLoading(true);

      // Fetch upcoming and live webinars
      const { data: upcomingData, error: upcomingError } = await supabase
        .from('webinars')
        .select('*')
        .in('status', ['upcoming', 'live'])
        .order('date', { ascending: true });

      if (upcomingError) throw upcomingError;

      // Fetch recorded webinars
      const { data: recordedData, error: recordedError } = await supabase
        .from('webinars')
        .select('*')
        .eq('status', 'recorded')
        .order('date', { ascending: false });

      if (recordedError) throw recordedError;

      // Fetch registration counts for upcoming webinars
      if (upcomingData && upcomingData.length > 0) {
        // Get registration counts for each webinar
        const registrationCounts = {};

        // Fetch registrations for each webinar individually
        for (const webinar of upcomingData) {
          const { count, error } = await supabase
            .from('webinar_registrations')
            .select('*', { count: 'exact', head: true })
            .eq('webinar_id', webinar.id);

          if (!error) {
            registrationCounts[webinar.id] = count || 0;
          }
        }

        // Create a compatible format for the rest of the code
        const registrationsData = Object.entries(registrationCounts).map(([webinar_id, count]) => ({
          webinar_id,
          count
        }));
        const registrationsError = null;

        if (!registrationsError && registrationsData) {
          // Add attendee counts to upcoming webinars
          const upcomingWithCounts = upcomingData.map(webinar => {
            const registrationInfo = registrationsData.find(r => r.webinar_id === webinar.id);
            return {
              ...webinar,
              attendees: registrationInfo ? registrationInfo.count : 0
            };
          });
          setUpcomingWebinars(upcomingWithCounts);
        } else {
          // If there's an error, just use the data without counts
          setUpcomingWebinars(upcomingData.map(webinar => ({ ...webinar, attendees: 0 })));
        }
      } else {
        setUpcomingWebinars([]);
      }

      // Add random view counts to recorded webinars for display purposes
      if (recordedData) {
        const recordedWithViews = recordedData.map(webinar => ({
          ...webinar,
          views: Math.floor(Math.random() * 1500) + 500 // Random number between 500-2000
        }));
        setRecordedWebinars(recordedWithViews);
      } else {
        setRecordedWebinars([]);
      }
    } catch (err: any) {
      console.error('Error fetching webinars:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRegisterClick = async (webinar: Webinar) => {
    // Save current scroll position before showing any modal
    saveScrollPosition();

    if (!user) {
      // If not logged in, show registration modal
      setSelectedWebinar(webinar);
      setShowRegistrationModal(true);
      return;
    }

    // If user is already registered, show cancellation modal
    if (userRegistrations[webinar.id]) {
      setSelectedWebinar(webinar);
      setShowCancellationModal(true);
      return;
    }

    // Check if user has a cancelled registration that can be reactivated
    try {
      const { data, error } = await supabase
        .from('webinar_registrations')
        .select('id, status')
        .eq('webinar_id', webinar.id)
        .eq('user_id', user.id)
        .eq('status', 'cancelled');

      if (error) throw error;

      // If there's a cancelled registration, reactivate it
      if (data && data.length > 0) {
        const { error: updateError } = await supabase
          .from('webinar_registrations')
          .update({ status: 'registered' })
          .eq('id', data[0].id);

        if (updateError) throw updateError;

        // Update local state
        setUserRegistrations(prev => ({
          ...prev,
          [webinar.id]: true
        }));

        // Restore scroll position
        restoreScrollPosition();

        // Refresh webinar data to update counts
        await fetchWebinars();

        // Show success message
        setSuccessMessage('Successfully registered for webinar');
        setTimeout(() => setSuccessMessage(null), 3000);
        return;
      }
    } catch (err) {
      console.error('Error checking cancelled registrations:', err);
    }

    // Otherwise show registration modal for new registration
    setSelectedWebinar(webinar);
    setShowRegistrationModal(true);
  };

  const handleCancelRegistration = async (webinarId: string) => {
    if (!user) return;

    try {
      setLoading(true);

      // Update the registration status to 'cancelled' instead of deleting
      const { error } = await supabase
        .from('webinar_registrations')
        .update({ status: 'cancelled' })
        .eq('webinar_id', webinarId)
        .eq('user_id', user.id);

      if (error) throw error;

      // Update local state
      setUserRegistrations(prev => {
        const updated = {...prev};
        delete updated[webinarId];
        return updated;
      });

      // Refresh webinar data to update counts
      await fetchWebinars();

      // Show success message
      setSuccessMessage('Registration successfully cancelled');
      setTimeout(() => setSuccessMessage(null), 3000);

      // Restore scroll position after all state updates
      setTimeout(() => {
        restoreScrollPosition();
      }, 100);
    } catch (err) {
      console.error('Error cancelling registration:', err);
      setError('Failed to cancel registration. Please try again.');
      setTimeout(() => setError(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  const handleRegistrationSuccess = () => {
    setShowRegistrationModal(false);

    // Restore scroll position immediately
    restoreScrollPosition();

    // Then show success modal and refresh data
    setTimeout(() => {
      setShowSuccessModal(true);
      // Refresh the webinar data to update registration counts
      fetchWebinars();
      // Refresh user registrations
      if (user) {
        fetchUserRegistrations();
      }
    }, 50);
  };

  const handleWatchRecording = (recordingUrl: string) => {
    window.open(recordingUrl, '_blank');
  };

  // Function to toggle description expansion
  const toggleDescription = (webinarId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [webinarId]: !prev[webinarId]
    }));
  };

  // Function to truncate text
  const truncateText = (text: string, wordCount: number = 15) => {
    if (!text) return '';

    // Handle case where there might be very long words
    const words = text.split(' ');

    // If text is already short enough, return it as is
    if (words.length <= wordCount && text.length < 100) return text;

    // Otherwise truncate
    return words.slice(0, wordCount).join(' ') + '...';
  };

  // Function to check if text needs truncation
  const needsTruncation = (text: string): boolean => {
    if (!text) return false;
    const words = text.split(' ');
    return words.length > 15 || text.length > 100;
  };

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-left">
            <h1 className="text-4xl font-bold mb-6">Webinars</h1>
            <p className="text-xl mb-10 text-blue-100">
              Join our expert-led webinars to stay updated with the latest in healthcare emergency response
            </p>
            <div className="relative w-full max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search webinars..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Error Message */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Upcoming Webinars */}
      {!loading && (
        <section id="upcoming-webinars" className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h2 className="text-2xl font-bold">
                {searchQuery.trim() !== '' ? `Search Results for "${searchQuery}"` : 'Upcoming Webinars'}
              </h2>
            </div>

            {filteredUpcomingWebinars.length === 0 ? (
              searchQuery.trim() !== '' ? (
                <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                  <p className="text-gray-600">
                    No upcoming webinars match your search "{searchQuery}". Try different keywords.
                  </p>
                </div>
              ) : (
              <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                <p className="text-gray-600">No upcoming webinars at this time. Check back soon!</p>
              </div>
              )
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredUpcomingWebinars.map((webinar) => (
                  <div key={webinar.id} className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-gray-100">
                    <div className="relative">
                      {webinar.image_url ? (
                        <img
                          src={webinar.image_url}
                          alt={webinar.title}
                          className="w-full h-52 object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.style.display = 'none';
                            target.parentElement!.innerHTML = `
                              <div class="w-full h-52 bg-blue-100 flex items-center justify-center">
                                <div class="text-center">
                                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mx-auto mb-2">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                  </svg>
                                  <p class="text-blue-600 font-medium text-sm px-2">${webinar.title}</p>
                                </div>
                              </div>
                            `;
                          }}
                        />
                      ) : (
                        <div className="w-full h-52 bg-blue-100 flex items-center justify-center">
                          <div className="text-center">
                            <MonitorPlay className="h-12 w-12 text-blue-600 mx-auto mb-2" />
                            <p className="text-blue-600 font-medium text-sm px-2">{webinar.title}</p>
                          </div>
                        </div>
                      )}
                      <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-semibold text-white ${webinar.status === 'live' ? 'bg-red-500' : 'bg-blue-500'} shadow-md`}>
                        {webinar.status === 'live' ? 'LIVE NOW' : 'UPCOMING'}
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent h-24"></div>
                    </div>
                    <div className="p-6 relative -mt-10">
                      <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 flex flex-col h-full">
                        <h3 className="text-lg font-bold mb-2 text-gray-800 line-clamp-2 min-h-[3.5rem]">{webinar.title}</h3>
                        <div className="text-gray-600 mb-4 text-sm min-h-[80px] overflow-hidden">
                          {expandedDescriptions[webinar.id] ? (
                            <>
                              <p className="break-words break-all">{webinar.description}</p>
                              <button
                                onClick={() => toggleDescription(webinar.id)}
                                className="text-blue-600 text-sm mt-1 hover:underline"
                              >
                                See Less
                              </button>
                            </>
                          ) : (
                            <>
                              <p className="break-words break-all">{truncateText(webinar.description)}</p>
                              {webinar.description && needsTruncation(webinar.description) && (
                                <button
                                  onClick={() => toggleDescription(webinar.id)}
                                  className="text-blue-600 text-sm mt-1 hover:underline"
                                >
                                  See More
                                </button>
                              )}
                            </>
                          )}
                        </div>

                        <div className="grid grid-cols-2 gap-3 mb-4 mt-auto">
                          <div className="flex items-center text-gray-600 text-sm">
                            <Calendar className="h-4 w-4 mr-2 text-blue-500" />
                            <span>{webinar.date}</span>
                          </div>
                          <div className="flex items-center text-gray-600 text-sm">
                            <Clock className="h-4 w-4 mr-2 text-blue-500" />
                            <span>{formatTimeWithAMPM(webinar.time || '')}</span>
                          </div>
                          <div className="flex items-center text-gray-600 text-sm">
                            <Users className="h-4 w-4 mr-2 text-blue-500" />
                            <span>{webinar.attendees || 0} registered</span>
                          </div>
                          <div className="flex items-center text-gray-600 text-sm">
                            <User className="h-4 w-4 mr-2 text-blue-500" />
                            <span>{webinar.speaker}</span>
                          </div>
                        </div>

                        {userRegistrations[webinar.id] && webinar.webinar_link && (
                          <div className="mb-4">
                            <a
                              href={webinar.webinar_link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                              <Video className="h-4 w-4 mr-2" />
                              <span>Join Webinar</span>
                            </a>
                          </div>
                        )}

                        <button
                          onClick={() => handleRegisterClick(webinar)}
                          className={`w-full h-10 py-2 px-4 rounded-lg font-medium text-sm transition duration-300 flex items-center justify-center ${userRegistrations[webinar.id]
                            ? 'bg-green-500 text-white hover:bg-green-600'
                            : 'bg-blue-500 text-white hover:bg-blue-600'}`}
                        >
                          {userRegistrations[webinar.id] ? (
                            <>
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Registered (Click again to cancel)
                            </>
                          ) : (
                            'Register Now'
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Recorded Webinars */}
      {!loading && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-2xl font-bold mb-8">Recorded Webinars</h2>

            {filteredRecordedWebinars.length === 0 ? (
              searchQuery.trim() !== '' ? (
                <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                  <p className="text-gray-600">
                    No recorded webinars match your search "{searchQuery}". Try different keywords.
                  </p>
                </div>
              ) : (
              <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                <p className="text-gray-600">No recorded webinars available yet. Check back soon!</p>
              </div>
              )
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredRecordedWebinars.map((webinar) => (
                  <div key={webinar.id} className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-gray-100">
                    <div className="relative group">
                      {webinar.image_url ? (
                        <img
                          src={webinar.image_url}
                          alt={webinar.title}
                          className="w-full h-52 object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.style.display = 'none';
                            target.parentElement!.innerHTML = `
                              <div class="w-full h-52 bg-purple-100 flex items-center justify-center">
                                <div class="text-center">
                                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-purple-600 mx-auto mb-2">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                  </svg>
                                  <p class="text-purple-600 font-medium text-sm px-2">${webinar.title}</p>
                                </div>
                              </div>
                            `;
                          }}
                        />
                      ) : (
                        <div className="w-full h-52 bg-purple-100 flex items-center justify-center">
                          <div className="text-center">
                            <MonitorPlay className="h-12 w-12 text-purple-600 mx-auto mb-2" />
                            <p className="text-purple-600 font-medium text-sm px-2">{webinar.title}</p>
                          </div>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-black/60 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <Play className="h-16 w-16 text-white opacity-90" />
                      </div>
                      <div className="absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-semibold text-white bg-purple-500 shadow-md">
                        RECORDED
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent h-24"></div>
                    </div>
                    <div className="p-6 relative -mt-10">
                      <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 flex flex-col h-full">
                        <h3 className="text-lg font-bold mb-2 text-gray-800 line-clamp-2 min-h-[3.5rem]">{webinar.title}</h3>
                        <div className="text-gray-600 mb-4 text-sm min-h-[80px] overflow-hidden">
                          {expandedDescriptions[webinar.id] ? (
                            <>
                              <p className="break-words break-all">{webinar.description}</p>
                              <button
                                onClick={() => toggleDescription(webinar.id)}
                                className="text-purple-600 text-sm mt-1 hover:underline"
                              >
                                See Less
                              </button>
                            </>
                          ) : (
                            <>
                              <p className="break-words break-all">{truncateText(webinar.description)}</p>
                              {webinar.description && needsTruncation(webinar.description) && (
                                <button
                                  onClick={() => toggleDescription(webinar.id)}
                                  className="text-purple-600 text-sm mt-1 hover:underline"
                                >
                                  See More
                                </button>
                              )}
                            </>
                          )}
                        </div>

                        <div className="grid grid-cols-2 gap-3 mb-4 mt-auto">
                          <div className="flex items-center text-gray-600 text-sm">
                            <Calendar className="h-4 w-4 mr-2 text-purple-500" />
                            <span>{webinar.date}</span>
                          </div>
                          <div className="flex items-center text-gray-600 text-sm">
                            <Clock className="h-4 w-4 mr-2 text-purple-500" />
                            <span>{webinar.duration}</span>
                          </div>
                          <div className="flex items-center text-gray-600 text-sm">
                            <Video className="h-4 w-4 mr-2 text-purple-500" />
                            <span>{webinar.views || 0} views</span>
                          </div>
                          <div className="flex items-center text-gray-600 text-sm">
                            <User className="h-4 w-4 mr-2 text-purple-500" />
                            <span>{webinar.speaker}</span>
                          </div>
                        </div>

                        <button
                          onClick={() => webinar.recording_url && handleWatchRecording(webinar.recording_url)}
                          className="w-full h-10 py-2 px-4 rounded-lg font-medium text-sm transition duration-300 flex items-center justify-center bg-purple-500 text-white hover:bg-purple-600"
                        >
                          <Play className="h-4 w-4 mr-2" />
                          Watch Recording
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>
      )}

      {/* Registration Modal */}
      {showRegistrationModal && selectedWebinar && (
        <WebinarRegistrationModal
          webinarId={selectedWebinar.id}
          webinarTitle={selectedWebinar.title}
          onClose={() => {
            setShowRegistrationModal(false);
            restoreScrollPosition();
          }}
          onSuccess={handleRegistrationSuccess}
        />
      )}

      {/* Success Modal */}
      {showSuccessModal && selectedWebinar && (
        <WebinarSuccessModal
          webinarTitle={selectedWebinar.title}
          webinarDate={selectedWebinar.date}
          webinarTime={selectedWebinar.time || ''}
          webinarLink={selectedWebinar.webinar_link}
          onClose={() => {
            setShowSuccessModal(false);
            restoreScrollPosition();
          }}
        />
      )}

      {/* Cancellation Modal */}
      {showCancellationModal && selectedWebinar && (
        <WebinarCancellationModal
          webinarTitle={selectedWebinar.title}
          webinarDate={selectedWebinar.date}
          webinarTime={selectedWebinar.time || ''}
          webinarSpeaker={selectedWebinar.speaker}
          onConfirm={() => {
            if (selectedWebinar) {
              // Close modal first
              setShowCancellationModal(false);
              // Then handle cancellation
              handleCancelRegistration(selectedWebinar.id);
            }
          }}
          onCancel={() => {
            setShowCancellationModal(false);
            restoreScrollPosition();
          }}
        />
      )}

      {/* Error Message */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{error}</span>
            <button onClick={() => setError(null)} className="ml-auto">
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {/* Success Message */}
      {successMessage && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg flex items-start">
            <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{successMessage}</span>
            <button onClick={() => setSuccessMessage(null)} className="ml-auto">
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}