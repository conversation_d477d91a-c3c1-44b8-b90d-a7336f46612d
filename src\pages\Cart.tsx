import { useEffect, useState } from 'react';
import { ShoppingCart, Trash2, Plus, Minus } from 'lucide-react';
import { useCartStore } from '../store/cartStore';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { supabase } from '../lib/supabase';

export default function Cart() {
  const navigate = useNavigate();
  const { items, itemsWithDetails, removeItem, updateQuantity, getCartWithProductDetails } = useCartStore();
  const user = useAuthStore((state) => state.user);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // --- Add state for cart-wide billing period ---
  const [cartBillingPeriod, setCartBillingPeriod] = useState<'monthly' | 'yearly'>('yearly');
  // --- End add state ---

  useEffect(() => {
    console.log('Cart component mounted, fetching product details');
    getCartWithProductDetails()
      .then(details => {
        console.log('Cart items with details:', details);
      })
      .catch(error => {
        console.error('Error fetching cart details:', error);
      });
  }, [getCartWithProductDetails]);

  // --- Update total calculation to use cartBillingPeriod ---
  const total = itemsWithDetails.reduce((sum, item) => {
    let price = item.product?.price || 0;
    // Use the global cartBillingPeriod state instead of item.billing_period
    const billingPeriod = cartBillingPeriod;

    // Apply discount if available based on the selected cartBillingPeriod
    if (billingPeriod === 'yearly') {
      // Yearly price is 10x monthly price (adjust if your logic differs)
      price = price * 12;
      // Apply yearly discount if available
      if (item.product?.yearly_discount) {
        price = price * (1 - (item.product.yearly_discount / 100));
      }
    } else {
      // Apply monthly discount if available
      if (item.product?.monthly_discount) {
        price = price * (1 - (item.product.monthly_discount / 100));
      }
    }

    // Ensure quantity is considered
    return sum + price * item.quantity;
  }, 0);
  // --- End update total calculation ---


  // --- Calculate the total cost if all items were billed monthly ---
  // Ensure this calculation is OUTSIDE handleSubscribe and BEFORE the return statement
  const monthlyEquivalentTotal = itemsWithDetails.reduce((sum, item) => {
    let price = item.product?.price || 0;
    // Apply monthly discount if available
    if (item.product?.monthly_discount) {
      price = price * (1 - (item.product.monthly_discount / 100));
    }
    return sum + price * item.quantity;
  }, 0);
  // --- End calculate monthly equivalent total ---

  // --- Calculate savings when yearly is selected ---
  // Ensure this calculation is OUTSIDE handleSubscribe and BEFORE the return statement
  const savings = cartBillingPeriod === 'yearly'
    ? (monthlyEquivalentTotal * 12) - total // Compare annual monthly cost vs discounted annual cost
    : 0;
  // --- End calculate savings ---

  const handleSubscribe = async () => {
    if (!user) {
      navigate('/login');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Get fresh session first
      const { data: { session: authSession }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !authSession) {
        throw new Error(sessionError?.message || 'Could not get user session.');
      }

      // --- Updated line_items mapping ---
      const line_items = itemsWithDetails.map(item => {
        // Determine which price ID to use based on the cartBillingPeriod state
        const priceId = cartBillingPeriod === 'yearly' // Use state here
          ? item.product?.stripe_yearly_price_id
          : item.product?.stripe_monthly_price_id;

        // Basic validation: Ensure product and the required price ID exist
        if (!item.product || !priceId) {
          console.error(`Missing product data or Stripe Price ID for product ${item.product_id} with period ${cartBillingPeriod}`); // Use state here
          return null;
        }

        return {
          price: priceId, // Use the determined price ID
          quantity: item.quantity
        };
      }).filter(item => item !== null); // Filter out any items that had missing data

      // Check if there are any valid line items left after filtering
      if (line_items.length === 0) {
        throw new Error('No valid items with Stripe Price IDs found in the cart.');
      }
      // --- End of updated line_items mapping ---


      const requestBody = {
        line_items: line_items, // Use the processed line_items
        mode: 'subscription',
        success_url: `${window.location.origin}/checkout/success`,
        cancel_url: `${window.location.origin}/cart`
      };

      console.log('Submitting checkout request:', JSON.stringify(requestBody, null, 2));

      const { data: checkoutSession, error: checkoutError } = await supabase.functions.invoke('stripe-checkout', {
        body: requestBody,
        headers: {
          'Authorization': `Bearer ${authSession.access_token}` // Use validated session
        }
      });

      console.log('Checkout response:', checkoutSession, checkoutError);

      if (checkoutError) throw checkoutError;
      if (checkoutSession?.url) {
        window.location.href = checkoutSession.url;
      } else {
        // Handle cases where the function might succeed but not return a URL
        throw new Error('Checkout session URL not received.');
      }

    } catch (err: any) { // Explicitly type err as any or unknown
      console.error("Checkout error:", err); // Log the full error
      setError(err.message || 'Checkout failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  // --- End update handleSubscribe ---


  if (items.length === 0 || itemsWithDetails.length === 0) {
    return (
      <div className="min-h-screen pt-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-20">
          <div className="text-center">
            <ShoppingCart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
            <p className="text-gray-600 mb-8">Looks like you haven't added any items to your cart yet.</p>
            <Link
              to="/solutions/grantready"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700"
            >
              View GrantReady™ Plans
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-20">
        <h1 className="text-3xl font-bold mb-8">Shopping Cart</h1>
        {error && (
          <div className="mb-4 p-4 bg-red-100 text-red-700 rounded">
            {error}
          </div>
        )}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
          <div className="p-6">
            {itemsWithDetails.map((item) => (
              <div key={`${item.product_id}-${cartBillingPeriod}`} className="flex items-center py-6 border-b border-gray-200 last:border-0">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">{item.product?.name || 'Product'}</h3>
                  {/* --- Update item display logic to use cartBillingPeriod --- */}
                  {cartBillingPeriod === 'yearly' ? ( // Use state here
                    <div>
                      {item.product?.yearly_discount ? (
                        <div className="flex items-center">
                          <p className="text-gray-600">
                            ${((item.product.price * 12) * (1 - (item.product.yearly_discount / 100))).toFixed(2)}/year
                          </p>
                          <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                            {item.product.yearly_discount}% off yearly
                          </span>
                        </div>
                      ) : (
                        <p className="text-gray-600">${(item.product?.price * 12 || 0).toFixed(2)}/year</p>
                      )}
                    </div>
                  ) : ( // Monthly display
                    <div>
                      {item.product?.monthly_discount ? (
                        <div className="flex items-center">
                          <p className="text-gray-600">
                            ${(item.product.price * (1 - (item.product.monthly_discount / 100))).toFixed(2)}/month
                          </p>
                          <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                            {item.product.monthly_discount}% off monthly
                          </span>
                        </div>
                      ) : (
                        <p className="text-gray-600">${(item.product?.price || 0).toFixed(2)}/month</p>
                      )}
                    </div>
                  )}
                  {/* --- End update item display logic --- */}
                </div>
                <div className="flex items-center space-x-4">
                  {/* --- Remove cartBillingPeriod from store function calls --- */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => updateQuantity(item.product_id, Math.max(1, item.quantity - 1))} // Remove cartBillingPeriod
                      className="p-1 rounded-full hover:bg-gray-100"
                    >
                      <Minus className="h-4 w-4 text-gray-600" />
                    </button>
                    <span className="w-8 text-center">{item.quantity}</span>
                    <button
                      onClick={() => updateQuantity(item.product_id, item.quantity + 1)} // Remove cartBillingPeriod
                      className="p-1 rounded-full hover:bg-gray-100"
                    >
                      <Plus className="h-4 w-4 text-gray-600" />
                    </button>
                  </div>
                  <button
                    onClick={() => removeItem(item.product_id)} // Remove cartBillingPeriod
                    className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                  {/* --- End update quantity/remove buttons --- */}
                </div>
              </div>
            ))}
          </div>
          {/* --- Add Billing Period Toggle --- */}
          <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm font-medium text-gray-700">Billing Period</span>
              <div className="flex items-center space-x-3">
                <span className={`text-sm font-medium ${cartBillingPeriod === 'monthly' ? 'text-blue-600' : 'text-gray-500'}`}>Monthly</span>
                <button
                  onClick={() => setCartBillingPeriod(cartBillingPeriod === 'monthly' ? 'yearly' : 'monthly')}
                  className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${cartBillingPeriod === 'yearly' ? 'bg-[#22c55e]' : 'bg-gray-300'}`}
                >
                  <span
                    className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform ${cartBillingPeriod === 'yearly' ? 'translate-x-6' : 'translate-x-1'}`}
                  />
                </button>
                <span className={`text-sm font-medium ${cartBillingPeriod === 'yearly' ? 'text-[#22c55e]' : 'text-gray-500'}`}>Yearly</span>
                {/* Optional: Display yearly discount info */}
                {/* {cartBillingPeriod === 'yearly' && (
                   <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Save with yearly!</span>
                )} */}
              </div>
            </div>
            {/* --- End Add Billing Period Toggle --- */}

            <div className="flex justify-between items-center text-lg font-semibold mt-4"> {/* Added mt-4 for spacing */}
              <span>Total ({cartBillingPeriod === 'yearly' ? 'Yearly' : 'Monthly'})</span>
              <span>${total.toFixed(2)}</span>
            </div>
            {/* --- Display Savings --- */}
            {/* This line should now correctly find the 'savings' variable */}
            {cartBillingPeriod === 'yearly' && savings > 0 && (
              <div className="text-right text-sm text-[#22c55e] font-medium mt-1">
                You save ${savings.toFixed(2)} by paying yearly!
              </div>
            )}
            {/* --- End Display Savings --- */}
          </div>
        </div>
        <div className="flex justify-end space-x-4">
          <Link
            to="/solutions/grantready"
            className="px-6 py-3 text-gray-700 hover:text-gray-900 font-medium"
          >
            Continue Shopping
          </Link>
          <button
            onClick={handleSubscribe}
            disabled={loading}
            className={`${loading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'} text-white px-6 py-3 rounded-lg font-semibold`}
          >
            {loading ? 'Processing...' : 'Proceed to Checkout'}
          </button>
          {/* <button
            onClick={async () => {
              const { data: { session } } = await supabase.auth.getSession();
              console.log('DEBUG JWT:', session?.access_token);
            }}
          >
            Log JWT
          </button> */}
        </div>
      </div>
    </div>
  );
}