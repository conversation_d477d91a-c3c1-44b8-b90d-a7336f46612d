import { useRef, useEffect } from 'react';

/**
 * Custom hook to save and restore scroll position
 * @returns Object with saveScrollPosition and restoreScrollPosition functions
 */
export function useScrollPosition() {
  const scrollPositionRef = useRef<number>(0);

  const saveScrollPosition = () => {
    scrollPositionRef.current = window.scrollY;
  };

  const restoreScrollPosition = () => {
    // Use multiple timeouts to ensure the DOM has updated before scrolling
    // and to handle various timing issues with React rendering
    const position = scrollPositionRef.current;

    // Immediate restore
    window.scrollTo({
      top: position,
      behavior: 'auto'
    });

    // Delayed restores to handle various timing issues
    setTimeout(() => {
      window.scrollTo({
        top: position,
        behavior: 'auto'
      });
    }, 0);

    setTimeout(() => {
      window.scrollTo({
        top: position,
        behavior: 'auto'
      });
    }, 50);

    setTimeout(() => {
      window.scrollTo({
        top: position,
        behavior: 'auto'
      });
    }, 100);
  };

  return {
    saveScrollPosition,
    restoreScrollPosition
  };
}
