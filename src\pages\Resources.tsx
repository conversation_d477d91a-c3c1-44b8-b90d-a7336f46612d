import React, { useState, useEffect } from 'react';
import { Library, Search, Tag, Clock, ArrowRight, MapPin, Download } from 'lucide-react';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';

interface FeaturedItem {
  id: string;
  title: string;
  description?: string;
  excerpt?: string;
  content?: string;
  category: string;
  created_at: string;
  image?: string;
  featured_image?: string;
  image_url?: string;
  organization?: string;
  impact?: string;
  date?: string;
  time?: string;
  location?: string;
  type?: string;
  pdf_url?: string;
  download_count?: number;
}

export default function Resources() {
  const [featuredItems, setFeaturedItems] = useState<FeaturedItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const resourceCategories = [
    {
      name: 'Blogs',
      path: '/blog',
      description: 'Latest articles and insights from our experts',
      table: 'blog_posts'
    },
    {
      name: 'Guides',
      path: '/guides',
      description: 'Step-by-step instructions and best practices',
      table: 'guides'
    },
    {
      name: 'Case Studies',
      path: '/case-studies',
      description: 'Real-world examples of our solutions in action',
      table: 'case_studies'
    },
    {
      name: 'Webinars',
      path: '/webinars',
      description: 'Educational sessions and presentations',
      table: 'webinars'
    },
    {
      name: 'Whitepapers',
      path: '/whitepapers',
      description: 'In-depth reports and research documents',
      table: 'whitepapers'
    },
    {
      name: 'Events',
      path: '/events',
      description: 'Upcoming conferences and training',
      table: 'events'
    }
  ];

  useEffect(() => {
    const fetchFeaturedItems = async () => {
      try {
        const items: FeaturedItem[] = [];

        for (const category of resourceCategories) {
          let query = supabase
            .from(category.table)
            .select('*')
            .order('created_at', { ascending: false })
            .limit(1);

          // Special handling for each resource type
          if (category.table === 'blog_posts') {
            query = query.eq('status', 'published');
          } else if (category.table === 'case_studies') {
            query = query.eq('is_public', true);
          }

          const { data, error } = await query;

          if (data && data.length > 0) {
            const item = {
              ...data[0],
              category: category.name,
              path: category.path
            };

            // Map different image field names to a common 'image' field
            if (item.featured_image) {
              item.image = item.featured_image;
            } else if (item.image_url) {
              item.image = item.image_url;
            }

            items.push(item);
          }
        }

        setFeaturedItems(items);
      } catch (error) {
        console.error('Error fetching featured items:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedItems();
  }, []);

  const [searchResults, setSearchResults] = useState<FeaturedItem[]>([]);
  const [showResults, setShowResults] = useState(false);

  // Add debounce function
  const debounce = (func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  };

  // Debounced search function
  const debouncedSearch = debounce(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    try {
      const searchPromises = resourceCategories.map(category =>
        supabase
          .from(category.table)
          .select('*')
          .ilike('title', `%${query}%`)
          .limit(5)
      );

      const results = await Promise.all(searchPromises);

      const foundItems: FeaturedItem[] = [];
      results.forEach(({ data }, i) => {
        if (data && data.length > 0) {
          data.forEach(item => {
            foundItems.push({
              ...item,
              category: resourceCategories[i].name,
              path: resourceCategories[i].path
            });
          });
        }
      });

      setSearchResults(foundItems);
      setShowResults(foundItems.length > 0);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
      setShowResults(false);
    }
  }, 300); // 300ms delay

  // Update the input onChange handler
  return (
    <div className="pt-16 bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-700 to-blue-900 text-white py-24 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-5xl font-bold mb-6">Resource Center</h1>
            <p className="text-xl mb-10 opacity-90">
              Discover our comprehensive library of resources to enhance your healthcare emergency response capabilities
            </p>

            {/* Search Bar with dropdown */}
            <form onSubmit={(e) => {
              e.preventDefault();
              debouncedSearch(searchQuery);
            }} className="relative max-w-2xl">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-400" />
                <input
                  type="text"
                  placeholder="Search resources..."
                  className="w-full pl-12 pr-4 py-4 rounded-lg bg-white/20 backdrop-blur-sm border border-white/30 text-white placeholder-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    debouncedSearch(e.target.value);
                  }}
                  onFocus={() => searchResults.length > 0 && setShowResults(true)}
                  onBlur={() => setTimeout(() => setShowResults(false), 200)}
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white"
                  aria-label="Search"
                >
                  <ArrowRight className="h-5 w-5" />
                </button>
              </div>

              {/* Search results dropdown - remains the same */}
              {showResults && searchResults.length > 0 && (
                <div className="absolute z-10 mt-2 w-full bg-white rounded-lg shadow-lg max-h-96 overflow-y-auto">
                  {searchResults.map((result) => (
                    // Update the search result item click handler
                    <div
                      key={`${result.category}-${result.id}`}
                      className="p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      onMouseDown={(e) => {
                        e.preventDefault(); // Prevent blur from firing immediately
                        navigate(`${result.path}/${result.id}`);
                        setShowResults(false);
                      }}
                    >
                      <div className="flex items-center">
                        <div className="flex-shrink-0 mr-3">
                          {result.image && (
                            <img
                              src={result.image}
                              alt={result.title}
                              className="h-10 w-10 rounded object-cover"
                            />
                          )}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{result.title}</p>
                          <p className="text-sm text-gray-500">{result.category}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </form>
          </div>
        </div>
      </section>

      {/* Categories Section - More visual hierarchy */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Browse by Category</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore our resources organized by topic
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {resourceCategories.map((category) => (
              <Link
                key={category.name}
                to={category.path}
                className="group p-8 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-100"
              >
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-lg bg-blue-50 text-blue-600 mr-4">
                    <Tag className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {category.name}
                  </h3>
                </div>
                <p className="text-gray-600 pl-14">{category.description}</p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Items Section - Enhanced card design */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Featured Resources</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Handpicked content from our experts to get you started
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {featuredItems.map((item) => (
                <div
                  key={`${item.category}-${item.id}`}
                  className="relative bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1 flex flex-col"
                >
                  <span className="absolute inline-flex items-center w-auto top-1 right-1 px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-3">
                      {item.category}
                    </span>
                  {item.image && (
                    <div className="h-48 overflow-hidden">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-full object-cover transition-transform duration-500 "
                      />
                    </div>
                  )}
                  <div className="p-6 flex-1 flex flex-col">

                    <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>

                    <p className="text-gray-600 mb-4 line-clamp-2 flex-1">
                      {item.description || item.excerpt}
                    </p>

                    {/* Metadata section */}
                    <div className="space-y-2 mb-4">
                      {item.date && (
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-2" />
                          {item.date}
                        </div>
                      )}
                      {item.location && (
                        <div className="flex items-center text-sm text-gray-500">
                          <MapPin className="h-4 w-4 mr-2" />
                          {item.location}
                        </div>
                      )}
                    </div>

                    <div className="mt-auto">
                      {item.category === 'Guides' && item.pdf_url ? (
                        <button
                          onClick={() => {
                            const link = document.createElement('a');
                            link.href = item.pdf_url;
                            link.download = item.pdf_url.split('/').pop() || 'guide.pdf';
                            link.target = '_blank';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                          className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download PDF
                        </button>
                      ) : item.category === 'Events' ? (
                        <Link
                          to="/events"
                          className="w-full flex items-center justify-center px-4 py-2 bg-white border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          View Events
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      ) : (
                        <Link
                          to={`${item.path}/${item.id}`}
                          className="w-full flex items-center justify-center px-4 py-2 bg-white border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          View Details
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>


    </div>
  );
}