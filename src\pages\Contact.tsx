import { useState, useRef } from 'react';
import { Mail, MapPin, Phone } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import HCaptcha from '@hcaptcha/react-hcaptcha';
import { supabase } from '../lib/supabase';
import Alert from '../components/ui/Alert';

const schema = yup.object().shape({
  name: yup.string().required('Full name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().matches(/^[0-9]+$/, 'Phone number must contain only numbers'),
  company: yup.string(),
  message: yup.string().required('Message is required')
});

export default function Contact() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const captcha = useRef<HCaptcha>(null);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    resolver: yupResolver(schema)
  });

  const onSubmit = async (data: any) => {
    if (!captchaToken) {
      setSubmitError('Please complete the hCaptcha verification');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      // First verify the captcha with your backend (optional additional verification)
      const { error: verifyError } = await supabase
        .rpc('verify_captcha', { token: captchaToken });

      if (verifyError) throw verifyError;

      // Then insert the form data
      const { error: insertError } = await supabase
        .from('contact_submissions')
        .insert([{
          ...data,
          captcha_token: captchaToken,
          submitted_at: new Date().toISOString()
        }]);

      if (insertError) throw insertError;

      // Send email to admin
      const { error: emailError } = await supabase.functions.invoke('send-contact-email', {
        body: {
          to: '', // Updated recipient
          subject: `New Contact Submission from ${data.name}`,
          html: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
              <h2 style="color: #2c3e50;">New Contact Form IRS Website</h2>
  
              <table cellpadding="8" cellspacing="0" border="1" style="font-family: Arial, sans-serif; color: #333;">
                <tbody>
                  <tr>
                    <td><strong>Name:</strong></td>
                    <td>${data.name}</td>
                  </tr>
                  <tr>
                    <td><strong>Email:</strong></td>
                    <td><a href="mailto:${data.email}">${data.email}</a></td>
                  </tr>
                  ${data.phone ? `
                  <tr>
                    <td><strong>Phone:</strong></td>
                    <td><a href="tel:${data.phone}">${data.phone}</a></td>
                  </tr>` : ''}
                  ${data.company ? `
                  <tr>
                    <td><strong>Company:</strong></td>
                    <td>${data.company}</td>
                  </tr>` : ''}
                </tbody>
              </table>

  
              <p><strong>Message:</strong></p>
              <div style="background: #f9f9f9; padding: 10px; border-left: 4px solid #3498db; white-space: pre-wrap;">
                ${data.message}
              </div>
            </div>

          `
        }
      });

      if (emailError) throw emailError;

      // Reset form on success
      reset();
      captcha.current?.resetCaptcha();
      setCaptchaToken(null);
      setSubmitSuccess(true);
    } catch (error: any) {
      console.error('Full error details:', error);
      setSubmitError(error.message || 'Failed to send message. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="pt-16">
      {/* Hero Section - unchanged */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold mb-4">Here For You</h1>
          <p className="text-xl text-blue-100">See how our solutions can work for you</p>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-16">
            {/* Contact Information - unchanged */}
            <div>
              <h2 className="text-2xl font-bold mb-8">Get in Touch</h2>
              <div className="space-y-6">
                <div className="flex items-start">
                  <MapPin className="h-6 w-6 text-blue-600 mt-1" />
                  <div className="ml-4">
                    <h3 className="font-semibold mb-1">Address</h3>
                    <p className="text-gray-600">
                      INTERNATIONAL RESPONDER SYSTEMS LLC<br />
                      157 E Main St<br />
                      Elkton, MD 21921-5977<br />
                      United States
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Mail className="h-6 w-6 text-blue-600 mt-1" />
                  <div className="ml-4">
                    <h3 className="font-semibold mb-1">Email</h3>
                    <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>

              <div className="mt-12">
                <img
                  src="https://images.unsplash.com/photo-1577962917302-cd874c4e31d2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                  alt="Office"
                  className="rounded-lg shadow-xl"
                />
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-gray-50 p-8 rounded-lg shadow-lg">
              <h2 className="text-2xl font-bold mb-6">Send us a Message</h2>

              {submitSuccess && (
                <Alert
                  type="success"
                  message="Message sent successfully!"
                  onClose={() => setSubmitSuccess(false)}
                />
              )}

              {submitError && (
                <Alert
                  type="error"
                  message={submitError}
                  onClose={() => setSubmitError('')}
                />
              )}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    {...register('name')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    {...register('email')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    {...register('phone')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name
                  </label>
                  <input
                    type="text"
                    id="company"
                    {...register('company')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    {...register('message')}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  ></textarea>
                  {errors.message && (
                    <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
                  )}
                </div>

                <div className="mt-4">
                  <HCaptcha
                    ref={captcha}
                    sitekey={import.meta.env.VITE_HCAPTCHA_SITE_KEY}
                    onVerify={(token) => setCaptchaToken(token)}
                    onExpire={() => setCaptchaToken(null)}
                  />
                </div>

                {submitError && (
                  <p className="mt-2 text-sm text-red-600">{submitError}</p>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-md font-semibold hover:bg-blue-700 transition duration-300 disabled:opacity-50"
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}