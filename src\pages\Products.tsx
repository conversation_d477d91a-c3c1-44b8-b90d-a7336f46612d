import { useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '../lib/supabase';
import { ShoppingCart, Check, Shield, Brain, Zap, Clock, Search } from 'lucide-react';
import { useCartStore } from '../store/cartStore';
import { useAuthStore } from '../store/authStore';
import { Link } from 'react-router-dom';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  pages?: string[];
  specifications: string[];
  theme_color?: string;
  monthly_discount?: number;
  yearly_discount?: number;
  created_at: string;
  updated_at?: string;
}

export default function Products() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  const [expandedDescriptions, setExpandedDescriptions] = useState<{ [key: string]: boolean }>({});
  const [addedProducts, setAddedProducts] = useState<{ [key: string]: boolean }>({});
  const [maxYearlyDiscount, setMaxYearlyDiscount] = useState<number>(0);
  const [searchQuery, setSearchQuery] = useState('');
  // No longer need filteredProducts state, useMemo will handle it

  const { addItem } = useCartStore();
  const user = useAuthStore((state) => state.user);

  // Fetch all products initially, remove searchQuery dependency
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching all products for the Products page');
      // Build the query - fetch all products tagged for 'Products' page
      let query = supabase
        .from('products')
        .select('*', { count: 'exact' })
        .contains('pages', '["Products"]'); // Keep filtering by page

      // Execute the query
      const { data: allProducts, error: fetchError } = await query;

      if (fetchError) {
        console.error('Error fetching products:', fetchError);
        setProducts([]);
        setError(`Error fetching products: ${fetchError.message || fetchError}`);
        return;
      }

      // If we have products for Products page
      if (allProducts && allProducts.length > 0) {
        // Sort products as before
        const sortedProducts = [...allProducts].sort((a, b) => {
          const colorA = a.theme_color || 'blue';
          const colorB = b.theme_color || 'blue';
          const colorPriority: { [key: string]: number } = {
            'purple': 1, 'orange': 2, 'yellow': 3,
          };
          const priorityA = colorPriority[colorA] || 999;
          const priorityB = colorPriority[colorB] || 999;
          if (priorityA !== priorityB) return priorityA - priorityB;
          return (a.price || 0) - (b.price || 0);
        });

        const highestDiscount = sortedProducts.reduce((max, product) => {
          const discount = product.yearly_discount || 0;
          return discount > max ? discount : max;
        }, 0);

        setMaxYearlyDiscount(highestDiscount);
        setProducts(sortedProducts); // Set all fetched products

      } else {
        console.log('No products found for Products page');
        setProducts([]);
      }
    } catch (err: unknown) {
      console.error('Error fetching products:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, []); // Remove searchQuery from dependencies

  // Fetch products only once on mount
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Filter products on the frontend using useMemo
  const filteredProducts = useMemo(() => {
    if (!searchQuery) {
      return products; // Return all products if search is empty
    }
    const lowerCaseQuery = searchQuery.toLowerCase();
    return products.filter(product =>
      product.name.toLowerCase().includes(lowerCaseQuery) ||
      product.description.toLowerCase().includes(lowerCaseQuery)
    );
  }, [products, searchQuery]); // Re-filter when products or searchQuery changes

  const handleAddToCart = (product: Product) => {
    console.log('Adding to cart:', product);

    // Add the product to cart with billing period
    addItem(product.id, billingInterval);
    // Set this product as recently added
    setAddedProducts(prev => ({
      ...prev,
      [product.id]: true
    }));

    // Reset the added state after 800 milliseconds
    setTimeout(() => {
      setAddedProducts(prev => ({
        ...prev,
        [product.id]: false
      }));
    }, 800);
  };

  const toggleDescription = (productId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  const scrollToProducts = () => {
    const productsSection = document.getElementById('products-section');
    if (productsSection) {
      // Get the y-position of the element
      const yPosition = productsSection.getBoundingClientRect().top + window.pageYOffset;

      // Subtract 100px to account for the fixed header and to show titles
      const offsetPosition = yPosition - 100;

      // Scroll to the adjusted position
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  // Function to truncate text
  const truncateText = (text: string, wordCount: number = 12) => {
    if (!text) return '';
    const words = text.split(' ');
    if (words.length <= wordCount) return text;
    return words.slice(0, wordCount).join(' ') + '...';
  };

  if (loading) {
    return (
      <div className="min-h-screen pt-16 bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Function to get color classes based on theme color
  const getColorClasses = (themeColor: string = 'blue') => {
    const colorMap = {
      blue: {
        gradient: 'from-blue-600 via-blue-700 to-blue-800',
        gradientLight: 'from-blue-400/30',
        gradientMedium: 'from-blue-500/30',
        gradientOverlay: 'from-blue-600/80 via-blue-700/80 to-blue-800/80',
        button: 'bg-blue-600 hover:bg-blue-700',
        border: 'border-blue-600',
        hoverBorder: 'hover:border-blue-100',
        text: 'text-blue-600',
        badge: 'bg-blue-100 text-blue-800',
        highlight: 'bg-blue-600'
      },
      orange: {
        gradient: 'from-orange-600 via-orange-700 to-orange-800',
        gradientLight: 'from-orange-400/30',
        gradientMedium: 'from-orange-500/30',
        gradientOverlay: 'from-orange-600/80 via-orange-700/80 to-orange-800/80',
        button: 'bg-orange-600 hover:bg-orange-700',
        border: 'border-orange-600',
        hoverBorder: 'hover:border-orange-100',
        text: 'text-orange-600',
        badge: 'bg-orange-100 text-orange-800',
        highlight: 'bg-orange-600'
      },
      purple: {
        gradient: 'from-purple-600 via-purple-700 to-purple-800',
        gradientLight: 'from-purple-400/30',
        gradientMedium: 'from-purple-500/30',
        gradientOverlay: 'from-purple-600/80 via-purple-700/80 to-purple-800/80',
        button: 'bg-purple-600 hover:bg-purple-700',
        border: 'border-purple-600',
        hoverBorder: 'hover:border-purple-100',
        text: 'text-purple-600',
        badge: 'bg-purple-100 text-purple-800',
        highlight: 'bg-purple-600'
      },
      yellow: {
        gradient: 'from-yellow-600 via-yellow-700 to-yellow-800',
        gradientLight: 'from-yellow-400/30',
        gradientMedium: 'from-yellow-500/30',
        gradientOverlay: 'from-yellow-600/80 via-yellow-700/80 to-yellow-800/80',
        button: 'bg-yellow-600 hover:bg-yellow-700',
        border: 'border-yellow-600',
        hoverBorder: 'hover:border-yellow-100',
        text: 'text-yellow-600',
        badge: 'bg-yellow-100 text-yellow-800',
        highlight: 'bg-yellow-600'
      }
    };

    return colorMap[themeColor as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white py-20 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-1/2 -left-1/2 w-full h-full rounded-full bg-gradient-to-br from-blue-400/30 to-transparent blur-3xl animate-[spin_30s_linear_infinite]"></div>
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full rounded-full bg-gradient-to-br from-blue-500/30 to-transparent blur-3xl animate-[spin_25s_linear_infinite_reverse]"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/80 via-blue-700/80 to-blue-800/80 backdrop-blur-[2px]"></div>
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Choose the Perfect Plan for Your Organization
            </h1>
            <p className="text-xl text-blue-100 mb-12">
              Flexible pricing options designed to scale with your needs. Start with our basic plan and upgrade as you grow.
            </p>
            <div className="bg-white/10 backdrop-blur-sm p-2 rounded-lg inline-flex">
              <button
                onClick={() => setBillingInterval('monthly')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'monthly'
                  ? 'bg-white text-blue-600'
                  : 'text-white hover:bg-white/10'
                  }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingInterval('yearly')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${billingInterval === 'yearly'
                  ? 'bg-white text-blue-600'
                  : 'text-white hover:bg-white/10'
                  }`}
              >
                Yearly
                <span className="ml-2 text-sm bg-green-500 text-white px-2 py-1 rounded-full">
                  Save up to {maxYearlyDiscount}%
                </span>
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {error && (
            <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-8">
              {error}
            </div>
          )}

          {/* Enhanced Search input */}
          <div className="mb-12"> {/* Increased bottom margin */}
            <div className="relative max-w-lg mx-auto"> {/* Slightly wider */}
              <input
                type="text"
                placeholder="Search plans by name or feature..." // More descriptive placeholder
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-5 py-3 pl-12 rounded-full bg-gray-50 border border-gray-200 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white shadow-sm transition-all" // Enhanced styling: rounded-full, bg-gray-50, border-gray-200, focus:bg-white, shadow-sm
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" /> {/* Centered icon */}
            </div>
          </div>

          <div id="products-section" className="grid md:grid-cols-3 gap-8">
            {/* Render filtered products */}
            {filteredProducts.length > 0 ? (
              filteredProducts.map((product) => (
                <div
                  key={product.id}
                  className={`bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 ${getColorClasses(product.theme_color).hoverBorder} transition-all duration-300 hover:shadow-2xl`} // Added hover:shadow-2xl
                >
                  <div className="p-6">
                    <h3 className="text-2xl font-bold mb-2">{product.name}</h3>
                    <div className="text-gray-600 mb-3 min-h-[3.5rem]">
                      {expandedDescriptions[product.id] ? (
                        <>
                          <p>{product.description}</p>
                          <button
                            onClick={() => toggleDescription(product.id)}
                            className={`${getColorClasses(product.theme_color).text} text-sm mt-1 hover:underline focus:outline-none`}
                          >
                            Read less
                          </button>
                        </>
                      ) : (
                        <>
                          <p>{truncateText(product.description)}</p>
                          {product.description && product.description.split(' ').length > 12 && (
                            <button
                              onClick={() => toggleDescription(product.id)}
                              className={`${getColorClasses(product.theme_color).text} text-sm hover:underline focus:outline-none`}
                            >
                              Read more
                            </button>
                          )}
                        </>
                      )}
                    </div>
                    <div className="mb-3 h-10 flex items-end">
                      {billingInterval === 'yearly' ? (
                        product.yearly_discount ? (
                          <div className="flex flex-col">
                            <div className="flex items-center">
                              <span className="text-4xl font-bold">
                                ${((product.price * 12) * (1 - product.yearly_discount / 100)).toFixed(2)}
                              </span>
                              <span className="text-gray-600">/year</span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-sm text-gray-500 line-through mr-2">
                                ${(product.price * 12).toFixed(2)}
                              </span>
                              <span className="text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                                Save {product.yearly_discount}%
                              </span>
                            </div>
                          </div>
                        ) : (
                          <div>
                            <span className="text-4xl font-bold">
                              ${(product.price * 12).toFixed(2)}
                            </span>
                            <span className="text-gray-600">/year</span>
                          </div>
                        )
                      ) : (
                        product.monthly_discount ? (
                          <div className="flex flex-col">
                            <div className="flex items-center">
                              <span className="text-4xl font-bold">
                                ${(product.price * (1 - product.monthly_discount / 100)).toFixed(2)}
                              </span>
                              <span className="text-gray-600">/month</span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-sm text-gray-500 line-through mr-2">
                                ${product.price.toFixed(2)}
                              </span>
                              <span className="text-sm bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                Save {product.monthly_discount}%
                              </span>
                            </div>
                          </div>
                        ) : (
                          <div>
                            <span className="text-4xl font-bold">
                              ${product.price.toFixed(2)}
                            </span>
                            <span className="text-gray-600">/month</span>
                          </div>
                        )
                      )}
                    </div>
                    {user ? (
                      <button
                        onClick={() => handleAddToCart(product)}
                        className={`w-full ${addedProducts[product.id] ? 'bg-green-600 hover:bg-green-700' : getColorClasses(product.theme_color).button} text-white py-3 px-6 rounded-lg font-semibold transition-colors`}
                        disabled={addedProducts[product.id]}
                      >
                        {addedProducts[product.id] ? (
                          <>
                            <Check className="inline-block h-5 w-5 mr-2" />
                            Added to Cart
                          </>
                        ) : (
                          <>
                            <ShoppingCart className="inline-block h-5 w-5 mr-2" />
                            Add to Cart
                          </>
                        )}
                      </button>
                    ) : (
                      <Link
                        to="/signup"
                        className={`block w-full text-center ${getColorClasses(product.theme_color).button} text-white py-3 px-6 rounded-lg font-semibold transition-colors`}
                      >
                        Sign Up to Subscribe
                      </Link>
                    )}
                  </div>
                  <div className="bg-white p-6">
                    <ul className="space-y-3">
                      {(product.specifications).map((feature: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))
            ) : (
              // If no products found after filtering
              <div className="col-span-3 text-center py-12">
                <div className="bg-gray-50 rounded-lg p-8 max-w-md mx-auto">
                  <h3 className="text-xl font-bold mb-4 text-gray-700">No Matching Products Found</h3>
                  <p className="text-gray-600 mb-6">
                    Your search for "{searchQuery}" did not match any products. Try adjusting your search terms.
                  </p>
                  <button
                    onClick={() => setSearchQuery('')} // Clear search button
                    className="inline-block bg-blue-600 text-white py-2 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                  >
                    Clear Search
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose GrantReady™?</h2>
            <p className="text-xl text-gray-600">
              Comprehensive features designed to streamline your grant management process
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <Shield className="h-12 w-12 text-blue-600 mb-6" />
              <h3 className="text-xl font-bold mb-4">Secure & Compliant</h3>
              <p className="text-gray-600">
                Enterprise-grade security with full compliance and audit trails
              </p>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <Brain className="h-12 w-12 text-blue-600 mb-6" />
              <h3 className="text-xl font-bold mb-4">AI-Powered</h3>
              <p className="text-gray-600">
                Smart automation and insights to optimize your grant process
              </p>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <Zap className="h-12 w-12 text-blue-600 mb-6" />
              <h3 className="text-xl font-bold mb-4">Lightning Fast</h3>
              <p className="text-gray-600">
                Rapid deployment and real-time updates across your organization
              </p>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <Clock className="h-12 w-12 text-blue-600 mb-6" />
              <h3 className="text-xl font-bold mb-4">24/7 Support</h3>
              <p className="text-gray-600">
                Round-the-clock expert support whenever you need it
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600">
              Find answers to common questions about our products
            </p>
          </div>

          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-bold mb-4">How do I get started?</h3>
              <p className="text-gray-600">
                Simply sign up for an account, choose your plan, and you'll have immediate access to our platform. Our team will guide you through the onboarding process.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-bold mb-4">Can I change plans later?</h3>
              <p className="text-gray-600">
                Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-bold mb-4">What payment methods do you accept?</h3>
              <p className="text-gray-600">
                We accept all major credit cards and can also arrange alternative payment methods for enterprise customers.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-bold mb-4">Is there a free trial?</h3>
              <p className="text-gray-600">
                Yes, we offer a 14-day free trial for all plans. No credit card required to start your trial.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 class="text-3xl font-bold mb-8">Ready to Get Started?</h2>
          <div class="flex justify-center space-x-6">
            {user ? (
              <button
                onClick={scrollToProducts}
                class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                Start Free Trial
              </button>
            ) : (
              <Link
                to="/signup"
                class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                Start Free Trial
              </Link>
            )}
            <Link
              to="/contact"
              class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
            >
              Contact Sales
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}