import React, { useState, useEffect } from 'react';
import { supabase } from '../../../lib/supabase'; // Adjust path if needed
import InvoicesTable from './InvoicesTable';
import InvoiceForm from './InvoiceForm';
// Define interfaces directly or import if they exist elsewhere
interface User { id: string; email: string; full_name: string | null; stripe_customer_id: string | null; }
interface Plan { id: string; name: string; /* add other relevant fields */ }

// --- Updated Invoice Interface ---
interface Invoice {
    id: string; // Stripe Invoice ID
    stripe_invoice_id: string;
    stripe_customer_id: string | null;
    customer_email: string | null; // Added
    customer_name: string | null; // Added
    status: string | null;
    amount_due: number | null;
    amount_paid: number | null;
    amount_remaining: number | null;
    currency: string | null;
    due_date: string | null; // ISO string
    invoice_pdf: string | null;
    hosted_invoice_url: string | null;
    created_at: string; // ISO string
    // Removed: user_id, updated_at, profiles
}
// --- End Updated Invoice Interface ---

import { PlusCircle, RefreshCw, AlertCircle, X } from 'lucide-react';

export default function InvoicesManagement() {
    const [invoices, setInvoices] = useState<Invoice[]>([]);
    const [users, setUsers] = useState<User[]>([]); // For the form
    const [products, setProducts] = useState<Plan[]>([]); // For the form
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isAdding, setIsAdding] = useState(false);
    // --- Updated Pagination State for Cursor Pagination ---
    const [itemsPerPage, setItemsPerPage] = useState(10); // Limit per request
    const [hasNextPage, setHasNextPage] = useState(false); // Replaces totalPages
    const [lastInvoiceIdStack, setLastInvoiceIdStack] = useState<(string | undefined)[]>([undefined]); // Stack to store 'starting_after' IDs for previous pages
    const [currentPageIndex, setCurrentPageIndex] = useState(0); // Index for the stack
    // Remove: currentPage, totalItems, totalPages
    // --- End Updated Pagination State ---

    useEffect(() => {
        // Fetch invoices when component mounts or itemsPerPage changes
        fetchInvoices(lastInvoiceIdStack[currentPageIndex], itemsPerPage); // Fetch based on current cursor
        fetchUsersAndProducts(); // Fetch data needed for the form
    }, [currentPageIndex, itemsPerPage]); // Refetch when page index or size changes

    const fetchInvoices = async (startingAfter: string | undefined, limit: number) => {
        setLoading(true);
        setError(null);
        try {
            // Invoke the Supabase Edge Function to fetch invoices
            const { data: functionData, error: functionError } = await supabase.functions.invoke('get-stripe-invoices', {
                // --- Pass cursor pagination params ---
                body: JSON.stringify({ limit: limit, starting_after: startingAfter }),
                // Remove: page, pageSize
            });

            if (functionError) {
                console.error("Edge function invocation error object:", functionError);
                throw new Error(`Edge function error: ${functionError.message}`);
            }

            // console.log('Raw data received from Edge Function:', JSON.stringify(functionData, null, 2));

            // --- Adjust data validation for cursor pagination ---
            if (!functionData || !Array.isArray(functionData.invoices) || typeof functionData.has_more !== 'boolean' || (functionData.invoices.length > 0 && typeof functionData.last_invoice_id !== 'string' && functionData.last_invoice_id !== null)) {
                console.error("Unexpected data format from Edge Function:", functionData);
                throw new Error('Received invalid data format from the server.');
            }
            // --- End Adjust data validation ---

            setInvoices(functionData.invoices as Invoice[]);
            // --- Set cursor pagination state ---
            setHasNextPage(functionData.has_more);

            // If navigating forward and there's a next page, add the last ID to the stack
            if (startingAfter === lastInvoiceIdStack[currentPageIndex] && functionData.has_more && functionData.last_invoice_id) {
                // Only add if it's a new page fetch and not already the last known ID
                if (lastInvoiceIdStack.length === currentPageIndex + 1) {
                    setLastInvoiceIdStack(prev => [...prev, functionData.last_invoice_id]);
                }
            }
            // Remove: setTotalItems, setTotalPages, currentPage adjustments
            // --- End Set cursor pagination state ---

        } catch (err: any) {
            console.error("Error fetching invoices via Edge Function:", err);
            setError(`Failed to load invoices: ${err.message}`);
            setInvoices([]); // Clear invoices on error
            setHasNextPage(false); // Reset pagination on error
        } finally {
            setLoading(false);
        }
    };

    const fetchUsersAndProducts = async () => {
        try {
            const { data: usersData, error: usersError } = await supabase.from('profiles').select('id, email, full_name, stripe_customer_id');
            if (usersError) throw usersError;
            setUsers(usersData || []);

            const { data: productsData, error: productsError } = await supabase.from('products').select('*'); // Fetch necessary product/price details
            if (productsError) throw productsError;
            setProducts(productsData || []);

        } catch (err: any) {
            console.error("Error fetching users/products:", err);
            setError(error ? `${error}, Failed to load data for form.` : 'Failed to load data for form.');
        }
    };

    const handleAddInvoice = (newInvoiceData: any) => {
        // Reset to first page and refetch after adding
        setCurrentPageIndex(0);
        setLastInvoiceIdStack([undefined]);
        fetchInvoices(undefined, itemsPerPage);
    };

    // Add a function to handle adding a new user to the users list
    const handleAddUser = (newUser: User) => {
        setUsers(prevUsers => [...prevUsers, newUser]);
    };

    // --- Updated Pagination Handlers for Cursor Pagination ---
    const handleNextPage = () => {
        if (hasNextPage) {
            setCurrentPageIndex(prev => prev + 1);
            // Fetching is handled by the useEffect hook reacting to currentPageIndex change
        }
    };

    const handlePreviousPage = () => {
        if (currentPageIndex > 0) {
            setCurrentPageIndex(prev => prev - 1);
            // Fetching is handled by the useEffect hook reacting to currentPageIndex change
        }
    };

    const handleItemsPerPageChange = (size: number) => {
        setItemsPerPage(size);
        setCurrentPageIndex(0); // Reset to first page
        setLastInvoiceIdStack([undefined]); // Reset cursor stack
        // Fetching is handled by the useEffect hook reacting to itemsPerPage change
    };
    // Remove: handlePageChange
    // --- End Updated Pagination Handlers ---


    return (
        <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800">Invoice Management</h2>
                <button
                    onClick={() => setIsAdding(true)}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150"
                >
                    <PlusCircle className="h-5 w-5 mr-2" />
                    Create Invoice
                </button>
            </div>

            {error && (
                <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-lg flex items-center">
                    <AlertCircle className="h-5 w-5 mr-2" />
                    {error}
                    <button className="ml-auto text-red-700 hover:text-red-900" onClick={() => setError(null)}>
                        <X className="h-5 w-5" />
                    </button>
                </div>
            )}

            {isAdding && (
                <InvoiceForm
                    isAdding={isAdding}
                    setIsAdding={setIsAdding}
                    users={users}
                    products={products}
                    onAddInvoice={handleAddInvoice}
                    setError={setError}
                    onAddUser={handleAddUser}
                />
            )}

            {loading && !isAdding ? (
                <div className="flex justify-center items-center h-64">
                    <RefreshCw className="h-8 w-8 text-blue-600 animate-spin" />
                </div>
            ) : !isAdding ? (
                <div className="mt-6">
                    <InvoicesTable
                        invoices={invoices}
                        // --- Pass updated/new pagination props ---
                        itemsPerPage={itemsPerPage}
                        onItemsPerPageChange={handleItemsPerPageChange}
                        // Pass handlers and state for Prev/Next buttons
                        currentPageIndex={currentPageIndex}
                        hasNextPage={hasNextPage}
                        onNextPage={handleNextPage}
                        onPreviousPage={handlePreviousPage}
                    // Remove: currentPage, totalPages, totalItems, onPageChange
                    // --- End Pass updated/new pagination props ---
                    />
                </div>
            ) : null}
        </div>
    );
}
