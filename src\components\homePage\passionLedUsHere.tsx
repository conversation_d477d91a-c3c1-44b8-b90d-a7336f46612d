import React from 'react';
import bottomImage from '../../assets/images/homepage/PassionedTeam.png';

// Arrow Icon for buttons
const ArrowRightIcon = ({ color = "currentColor" }: { color?: string }) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2.5"
    strokeLinecap="round"
    strokeLinejoin="round"
    style={{ display: 'block' }} // Prevents extra space below inline-flex if parent is
  >
    <line x1="5" y1="12" x2="19" y2="12"></line>
    <polyline points="12 5 19 12 12 19"></polyline>
  </svg>
);

const PassionLedUsHere: React.FC = () => {
  const [isMobile, setIsMobile] = React.useState(false);
  const hereColor = '#A892F7'; // Lavender color for "here" and right arrow
  const containerBgColor = '#F8F9FA'; // Very light gray for page background effect
  const textDarkGray = '#3C4043'; // A common dark gray for text
  const textBlack = '#000000';

  // Track screen size for responsive design
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <>
    <div style={{
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
      backgroundColor: containerBgColor,
      padding: isMobile ? '80px 20px 40px 20px' : '80px 20px 120px 20px', // Less bottom padding on mobile
      textAlign: 'center',
      position: 'relative',
      overflowX: 'hidden', // Prevent horizontal scroll from wide arrows if they extend
    }}>
      {/* Content Wrapper for centering and max-width */}
      <div style={{
        maxWidth: '960px',
        margin: '0 auto',
        position: 'relative',
        zIndex: 1, // Ensures content is above decorative arrows
      }}>
        <h1 style={{
          fontSize: 'clamp(1.8rem, 4.5vw, 2.8rem)',
          fontWeight: 700,
          color: textBlack,
          margin: '0 0 25px 0',
          lineHeight: 1.1,
          letterSpacing: '-0.02em',
        }}>
          Passion Led us <span style={{ color: hereColor }}>here</span>
        </h1>

        <p style={{
          fontSize: 'clamp(1.15rem, 2.8vw, 1.45rem)',
          color: textBlack,
          fontWeight: 600,
          maxWidth: '920px',
          margin: '0 auto 5px auto',
          lineHeight: 1.35,
        }}>
          Our capabilities are transferable across many public and private sector agencies.
        </p>
        <p style={{
          fontSize: 'clamp(1.15rem, 2.8vw, 1.45rem)',
          color: textBlack,
          fontWeight: 600,
          maxWidth: '720px',
          margin: '0 auto 15px auto',
          lineHeight: 1.65,
        }}>
          We are versed in a variety of response support.
        </p>

        <p style={{
          fontSize: 'clamp(1.15rem, 2.8vw, 1.45rem)',
          color: textBlack,
          fontWeight: 600,
          maxWidth: '960px',
          margin: '0 auto 25px auto',
          lineHeight: 1.7,
        }}>
          Let us help you meet your goals with significant knowledge and accuracy that counts.
        </p>

        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '25px',
          flexWrap: 'wrap', // Allow buttons to wrap on smaller screens
        }}>
          <button style={{
            backgroundColor: textBlack,
            color: '#FFFFFF',
            border: `2px solid ${textBlack}`,
            padding: '4px 1px 4px 5px', // T,R,B,L - more padding left of text
            borderRadius: '8px',
            fontSize: 'clamp(0.95rem, 2.2vw, 1.05rem)',
            fontWeight: 600,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            transition: 'background-color 0.2s ease, border-color 0.2s ease',
            letterSpacing: '0.5px',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = '#333333';
            e.currentTarget.style.borderColor = '#333333';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = textBlack;
            e.currentTarget.style.borderColor = textBlack;
          }}
          >
            Discover Our Solution
            <span style={{
                backgroundColor: '#FFFFFF',
                padding: '7px',
                borderRadius: '6px',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '12px', // Space between text and icon box
                lineHeight: 0, // Helps align SVG by removing extra space
            }}>
              <ArrowRightIcon color={textBlack} />
            </span>
          </button>

          <button style={{
            backgroundColor: '#FFFFFF',
            color: textBlack,
            border: `2px solid #9CA3AF`,
            // padding: '14px 18px 14px 28px', // T,R,B,L
            padding: '2px 1px 2px 8px', // T,R,B,L - more padding left of text
            borderRadius: '8px',
            fontSize: 'clamp(0.95rem, 2.2vw, 1.05rem)',
            fontWeight: 600,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            transition: 'background-color 0.2s ease, color 0.2s ease',
            letterSpacing: '0.5px',
          }}
          onMouseOver={(e) => { e.currentTarget.style.backgroundColor = '#F0F0F0'; }}
          onMouseOut={(e) => { e.currentTarget.style.backgroundColor = '#FFFFFF'; }}
          >
            See A Demo
            <span style={{
                padding: '7px', // Keep padding consistent for alignment
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '12px',
                lineHeight: 0,
            }}>
              <ArrowRightIcon color={textBlack} />
            </span>
          </button>
        </div>
      </div>

      {/* Bottom Left Arrow - Only show on desktop */}
      {!isMobile && (
        <div style={{
          position: 'absolute',
          bottom: '20px',
          left: '60px',
          zIndex: 0,
        }}>
          <svg width="30" height="86" viewBox="0 0 30 86" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16.6102 55.6496C19.6347 61.2218 22.3744 67.0354 24.2982 73.1932C25.3225 76.4711 26.0922 79.8243 26.6002 83.2227L26.6747 83.6848L23.3464 83.571C24.778 76.5705 25.7706 69.5037 25.9079 62.4283C26.0968 55.3512 25.2513 48.3024 23.7915 41.3876L27.9757 41.0173C27.9429 42.6218 27.8945 44.2368 27.7922 45.8699C27.7419 46.6848 27.6753 47.501 27.6098 48.2883C27.5446 49.0764 27.4857 49.8416 27.4413 50.6142C27.3734 51.768 27.3325 52.9361 27.3004 54.1101L27.2735 55.0211C27.259 55.4023 27.2272 55.7324 27.1921 56.0634C27.1224 56.72 27.0254 57.3378 26.9219 57.9591C26.7082 59.1947 26.4558 60.407 26.1386 61.6269C25.8255 62.9197 25.397 64.1817 24.8585 65.3978C24.6673 65.8145 24.3486 66.1592 23.9489 66.3817C23.5492 66.6042 23.0895 66.6929 22.6367 66.6349C22.1839 66.5769 21.7617 66.3752 21.4316 66.0591C21.1016 65.7431 20.881 65.3293 20.802 64.878L20.7971 64.8483C20.093 60.6738 19.237 56.5258 18.2871 52.3992C17.3089 48.2792 16.2231 44.1864 15.03 40.1207C14.4041 38.0978 13.8022 36.0667 13.123 34.0606C12.4687 32.046 11.7257 30.0624 11.0138 28.0682C9.53014 24.1014 7.94074 20.1774 6.24558 16.2962C5.56739 14.8119 4.94494 13.2934 4.29149 11.7934C3.96762 11.0418 3.64442 10.2918 3.32188 9.54351C3.01104 8.78976 2.68336 8.047 2.35634 7.30852C1.12945 4.46723 0.59555 2.48587 0.566212 1.08457C0.541151 0.0669931 0.947547 -0.153239 1.64896 0.362958C2.34298 0.882241 3.36224 2.12384 4.47286 4.05746C4.8216 4.67037 5.1699 5.30231 5.51576 5.9465C5.86329 6.58987 6.20656 7.24639 6.52712 7.91757C6.84958 8.58771 7.16754 9.26329 7.47953 9.93773L7.94278 10.9474C8.08799 11.2866 8.23246 11.6239 8.3762 11.9592C11.0306 18.1272 13.4674 24.369 15.6867 30.6844C17.9125 36.9974 19.9317 43.3822 21.7443 49.8388C22.2628 51.6163 22.7275 53.4476 23.1959 55.2659C23.4235 56.1767 23.6182 57.0919 23.8128 57.9974C24.0026 58.9038 24.1861 59.8021 24.3247 60.6931L24.8915 64.1081L21.1112 63.5965C21.4844 62.806 21.791 61.9856 22.0277 61.1439C22.2923 60.2511 22.5204 59.3267 22.7161 58.3981C22.91 57.4714 23.0754 56.5282 23.1813 55.6307C23.1968 55.5163 23.2047 55.411 23.215 55.3031C23.2253 55.1951 23.2366 55.0848 23.2396 54.9922C23.2517 54.8225 23.2586 54.5484 23.2661 54.3007L23.3158 52.7951C23.3529 51.7898 23.3989 50.7778 23.4676 49.7619C23.5338 48.7443 23.6272 47.7391 23.7027 46.7707C23.8569 44.8392 23.9249 42.8969 23.9778 40.9283L23.9788 40.8898C23.9913 40.4221 24.1761 39.9753 24.4975 39.6358C24.8189 39.2964 25.2541 39.0883 25.7188 39.0519C26.1835 39.0156 26.6448 39.1535 27.0133 39.439C27.3819 39.7245 27.6316 40.1373 27.7142 40.5975C28.1383 42.9608 28.5206 45.3323 28.7569 47.7225C29.2167 52.029 29.4245 56.3595 29.3792 60.6916C29.3277 65.0184 28.9977 69.3379 28.3911 73.6232C27.8952 77.1902 27.2412 80.7245 26.4875 84.2256L26.4833 84.2454C26.4075 84.5967 26.2105 84.9101 25.9272 85.1302C25.6438 85.3504 25.2923 85.4631 24.9347 85.4485C24.5771 85.4339 24.2364 85.2929 23.9728 85.0504C23.7093 84.8079 23.5397 84.4795 23.4942 84.1233C22.7819 78.534 21.0687 73.0699 18.8194 67.8275C16.5699 62.5761 13.8019 57.5247 10.8684 52.5553L10.4329 51.8118L10.4136 51.7787C10.2635 51.5225 10.2123 51.2198 10.2697 50.9279C10.3272 50.636 10.4892 50.3751 10.7252 50.1947C10.9611 50.0142 11.2546 49.9268 11.5501 49.9489C11.8455 49.971 12.1224 50.1011 12.3283 50.3146C13.3557 51.3798 14.2806 52.4696 15.1581 53.5604C16.0361 54.6498 16.8536 55.7476 17.6278 56.8399C18.4034 57.931 19.14 59.0128 19.8376 60.0852C20.5463 61.1476 21.1786 62.2311 21.8361 63.2529C22.4642 64.2461 22.6907 64.7771 22.4678 64.8614C22.255 64.9379 21.5707 64.5784 20.5035 63.6964C20.1312 63.3808 19.7566 63.0634 19.3799 62.7442C19.0144 62.4156 18.6957 62.0463 18.3521 61.6953C17.6837 60.9813 17.0104 60.262 16.3323 59.5373C15.0043 58.0959 13.6625 56.6395 12.3241 55.1868C11.539 54.3441 10.7554 53.492 9.95995 52.6925L13.2916 50.0606C14.3983 51.8926 15.4779 53.7749 16.5412 55.6547C16.5632 55.653 16.5839 55.6516 16.6102 55.6496Z" fill="black"/>
          </svg>
        </div>
      )}

      {/* Bottom Right Arrow - Only show on desktop */}
      {!isMobile && (
        <div style={{
          position: 'absolute',
          bottom: '5px',
          right: '60px',
          zIndex: 0,
        }}>
          <svg width="180" height="184" viewBox="0 0 180 184" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M101.981 68.5582C97.0698 72.5953 92.7258 77.2809 89.0668 82.488C88.6469 83.0907 88.2414 83.7046 87.8279 84.3126L86.6445 86.1755L85.5277 88.0787L84.4623 90.0076L87.4431 90.9141C87.4791 90.5044 87.5148 90.0961 87.5504 89.6892C87.5977 88.846 87.6286 88.0001 87.7351 87.1623C87.8231 86.3224 87.9136 85.4828 88.025 84.6458C88.2234 82.9685 88.4766 81.2982 88.7646 79.6346C89.0425 77.9692 89.3605 76.3114 89.7185 74.6613C90.0707 73.0107 90.452 71.3667 90.8797 69.7349C92.5793 63.2132 94.7255 56.8179 97.3029 50.5947L93.4378 49.1335C92.1357 52.7854 90.8505 56.5519 90.127 60.5667C89.7149 62.761 89.5458 64.9947 89.6228 67.2273C89.624 67.5101 89.6487 67.7945 89.6776 68.0792L89.758 68.9327C89.8148 69.5014 89.9354 70.0689 90.0245 70.6364C90.138 71.2016 90.296 71.7612 90.4316 72.3233C90.6173 72.8732 90.814 73.4204 91.0081 73.9667C91.1379 74.3319 91.3696 74.6522 91.6754 74.8895C91.9813 75.1267 92.3484 75.2707 92.7331 75.3046C93.1178 75.3384 93.5038 75.2605 93.8452 75.0802C94.1866 74.8999 94.4689 74.6247 94.6585 74.2876L94.6928 74.2263C95.7472 72.3446 96.672 70.3876 97.7419 68.5116C98.7886 66.6225 99.8935 64.7671 100.981 62.9004C102.089 61.0462 103.233 59.2145 104.413 57.4054C104.997 56.4968 105.58 55.5873 106.184 54.6925C106.783 53.7934 107.381 52.8941 107.992 52.0039C110.443 48.4475 112.975 44.9488 115.586 41.5077C118.205 38.0744 120.917 34.7142 123.683 31.4007C125.763 28.9357 127.797 26.3894 129.836 23.9347C131.76 21.5589 132.806 19.8298 133.231 18.5375C133.864 16.666 131.996 17.378 128.855 20.4461C126.815 22.4941 124.883 24.6472 123.066 26.8968C114.704 37.1191 106.928 47.7309 99.6362 58.7058C97.5929 61.7143 95.4902 64.8709 93.7476 67.9549C92.9055 69.3974 92.0826 70.8538 91.2788 72.3241L94.6463 72.5614C93.9942 70.8701 93.6133 69.0863 93.5181 67.2764C93.4309 65.4042 93.5544 63.5286 93.8863 61.6851C94.5285 57.9255 95.8028 54.2077 97.1217 50.4562L97.1399 50.4041C97.3021 49.9425 97.2754 49.4349 97.0656 48.9922C96.8559 48.5495 96.4802 48.2078 96.0207 48.0419C95.5612 47.876 95.0554 47.8994 94.6139 48.107C94.1724 48.3145 93.8311 48.6893 93.6648 49.1494L92.8378 51.4379C92.5696 52.203 92.3173 52.9732 92.0596 53.7407C89.4091 61.8018 87.3922 70.0603 86.0276 78.4405L85.7838 79.9611L85.5654 81.4855C85.4138 82.5008 85.2739 83.5178 85.1456 84.5363C85.0029 85.553 84.9068 86.5754 84.7858 87.5945C84.643 88.6115 84.525 89.6319 84.4403 90.6551C84.4135 90.9793 84.4979 91.3032 84.6793 91.5732C84.8608 91.8432 85.1285 92.0431 85.4382 92.1399C85.7479 92.2366 86.081 92.2244 86.3824 92.1052C86.6837 91.9861 86.9353 91.7671 87.0955 91.4846L87.1058 91.4656L88.0885 89.7273L89.1238 88.0198L90.202 86.3388C90.5688 85.7833 90.9545 85.2405 91.3297 84.6903C95.9592 78.1377 101.739 72.4872 108.386 68.0144C108.698 67.8041 109.009 67.5946 109.308 67.3932L109.349 67.3654C109.603 67.1945 109.781 66.9312 109.845 66.631C109.909 66.3308 109.854 66.0172 109.692 65.7562C109.53 65.4953 109.274 65.3074 108.977 65.2323C108.68 65.1571 108.366 65.2005 108.101 65.3533C105.7 66.7359 103.428 68.0932 101.193 69.3088C98.9613 70.5315 96.7614 71.6342 94.6629 72.7229C93.6299 73.2519 93.1828 73.6334 93.3394 73.8573C93.4147 73.9589 93.6475 74.0421 94.0267 74.0596C94.559 74.0867 95.0926 74.0468 95.6148 73.9408C96.0865 73.8424 96.559 73.7287 97.0352 73.6167C97.51 73.5011 97.9763 73.3455 98.4506 73.2078C99.3924 72.9084 100.342 72.5935 101.282 72.231C103.166 71.5076 105.01 70.6834 106.805 69.762C107.776 69.2711 108.732 68.7519 109.688 68.22L107.559 64.6228C105.662 65.839 103.821 67.1912 102.042 68.5873C102.022 68.5768 102.003 68.5674 101.981 68.5582Z" fill="#797EEC"/>
            <path d="M119.75 123.11C114.517 127.545 109.94 132.706 106.159 138.438C104.149 141.456 102.355 144.612 100.79 147.885C100.504 148.501 100.195 149.106 99.9272 149.731L99.1366 151.612L102.013 152.357C102.097 150.403 102.351 148.469 102.558 146.533C102.778 144.598 102.986 142.664 103.242 140.738C103.743 136.885 104.291 133.045 104.974 129.24C105.663 125.437 106.468 121.665 107.468 117.965C108.438 114.286 109.676 110.684 111.172 107.187L107.408 106.004C107.194 106.969 106.986 107.937 106.785 108.908C106.583 109.88 106.385 110.855 106.193 111.834C105.824 113.7 105.487 115.583 105.198 117.483C104.907 119.381 104.664 121.298 104.509 123.235C104.356 125.181 104.3 127.134 104.342 129.086C104.342 129.575 104.381 130.068 104.399 130.558L104.435 131.294L104.497 132.031C104.619 133.01 104.8 133.983 104.921 134.961C104.974 135.388 105.166 135.786 105.468 136.093C105.769 136.4 106.163 136.599 106.588 136.659C107.012 136.718 107.445 136.635 107.817 136.422C108.189 136.209 108.481 135.879 108.647 135.482L108.655 135.461C110.151 131.857 111.696 128.27 113.359 124.737C115.059 121.222 116.801 117.726 118.697 114.312C122.449 107.463 126.521 100.806 130.913 94.3404L133.813 89.982C134.787 88.5325 135.755 87.0913 136.717 85.6583C137.499 84.5199 138.22 83.3398 138.875 82.1231C139.319 81.3245 139.681 80.4826 139.956 79.6101C140.25 78.6082 139.946 78.3665 139.175 78.8223C138.639 79.1688 138.14 79.5707 137.687 80.0216C136.958 80.7175 136.271 81.4574 135.631 82.2371C135.15 82.8115 134.665 83.4024 134.177 84.0099C133.69 84.6155 133.227 85.2516 132.753 85.8838C132.283 86.5191 131.811 87.1566 131.354 87.8006C130.906 88.4505 130.461 89.0951 130.02 89.7346C129.002 91.1991 128.038 92.6996 127.046 94.1792L125.607 96.4312C125.129 97.1824 124.644 97.929 124.181 98.6898L122.775 100.96C122.304 101.715 121.857 102.484 121.395 103.245C120.467 104.763 119.583 106.307 118.684 107.842C118.229 108.606 117.805 109.388 117.363 110.16C116.929 110.936 116.479 111.704 116.056 112.486C115.205 114.047 114.338 115.6 113.512 117.176C111.803 120.297 110.221 123.484 108.578 126.647L106.775 130.254C106.192 131.473 105.613 132.696 105.056 133.912L108.795 134.413C108.763 134.11 108.732 133.809 108.7 133.508C108.678 133.207 108.651 132.908 108.635 132.609C108.626 132.309 108.602 132.011 108.601 131.714C108.574 131.418 108.533 131.126 108.516 130.831C108.349 128.85 108.314 126.86 108.411 124.875C108.5 122.876 108.668 120.866 108.928 118.857C109.444 114.835 110.265 110.818 111.067 106.769L111.073 106.738C111.157 106.31 111.08 105.864 110.856 105.489C110.632 105.113 110.276 104.835 109.859 104.708C109.442 104.581 108.993 104.614 108.6 104.801C108.206 104.988 107.897 105.316 107.731 105.72C106.234 109.437 105.037 113.27 104.154 117.181C103.127 121.654 102.338 126.165 101.681 130.685C101.01 135.203 100.51 139.735 100.041 144.251L99.6431 148.158L99.4462 150.109L99.1719 152.05C99.1264 152.373 99.1986 152.702 99.3752 152.976C99.5519 153.25 99.8211 153.451 100.133 153.543C100.446 153.634 100.78 153.609 101.075 153.473C101.37 153.336 101.606 153.097 101.739 152.8L101.773 152.724C102.096 152 102.436 151.284 102.753 150.558L103.796 148.421L104.914 146.321C105.316 145.637 105.693 144.939 106.093 144.253C107.707 141.516 109.498 138.888 111.455 136.387C115.339 131.359 119.942 126.935 125.115 123.258L125.143 123.239C125.4 123.059 125.578 122.786 125.639 122.477C125.699 122.168 125.638 121.847 125.468 121.581C125.298 121.316 125.032 121.127 124.726 121.054C124.42 120.981 124.098 121.03 123.828 121.191C123.326 121.489 122.817 121.777 122.321 122.084C119.635 123.713 117.072 125.539 114.654 127.547C112.433 129.37 110.33 131.332 108.357 133.422C107.496 134.328 107.17 134.842 107.378 134.955C107.581 135.061 108.343 134.798 109.599 134.084C110.034 133.821 110.472 133.556 110.912 133.29C111.349 133.02 111.766 132.721 112.196 132.437C113.054 131.868 113.918 131.295 114.789 130.718C116.533 129.577 118.301 128.444 120.082 127.361C121.867 126.28 123.636 125.127 125.458 124.071L123.251 120.526L121.507 121.806C120.93 122.237 120.379 122.697 119.814 123.138C119.793 123.128 119.774 123.119 119.75 123.11Z" fill="black"/>
          </svg>
        </div>
      )}

    </div>

    {/* Bottom Image - Full Width - Outside main container */}
    <div style={{
      width: '100%',
      marginTop: '0px', // No gap between sections
    }}>
      <img
        src={bottomImage}
        alt="Bottom decorative image"
        style={{
          width: '100%',
          height: 'auto',
          display: 'block',
        }}
      />
    </div>
    </>
  );
};

export default PassionLedUsHere;