import React from 'react';
import { Building2, Award, Users, Target, Heart, Zap, ArrowRight, Book } from 'lucide-react';
import Believe from '.././assets/images/believe.png';
import Envision from '.././assets/images/Envision.png';
import Accompolishments from '.././assets/images/accompishments.png';
import Clients from '../assets/images/clients.png';
import Portfolio from '../assets/images/portfolio.png';
import Value1 from '../assets/images/value1.avif'
import Value2 from '../assets/images/value2.avif'
import Value3 from '../assets/images/value3.avif'
import Acc1 from '../assets/images/acc1.avif'
import Acc2 from '../assets/images/acc2.avif'

import All1 from '../assets/images/all1.avif'
import All2 from '../assets/images/all2.avif'
import All3 from '../assets/images/all3.avif'
import All4 from '../assets/images/all4.avif'
import All5 from '../assets/images/all5.avif'

import Certif1 from '../assets/images/certif.avif'

import { Link } from 'react-router-dom';

// Logo and Title Component
function IRSLogoTitle() {
  return (
    <div className="relative flex items-center gap-6 mb-8">
      <div className="flex flex-col justify-center">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-extrabold leading-tight text-white">
        <span className="text-red-500">I</span>nternational <span className="text-red-500">R</span>esponder<br />
          <span className="text-red-500">S</span>ystems
        </h1>
        <div className="text-white text-base sm:text-lg mt-1 font-medium">
          Public Health and Healthcare Responder Systems
        </div>
      </div>
    </div>
  );
}

export default function About() {
  const stats = [
    { label: 'Years of Experience', value: '10+' },
    { label: 'Clients Served', value: '1000+' },
    { label: 'Team Members', value: '100+' },
    { label: 'Success Rate', value: '99%' }
  ];

  const values = [
    { image: Value1, title: 'Working With Us', description: 'Prepare your team to be : Take proactive steps, Think critically, React to crucial situations' },
    { image: Value2, title: 'Prepare for the Worst ', description: 'Our team of subject matter experts with proven track records of success ready to train and serve you for anything that can confront you.' },
    { image: Value3, title: 'Exceeding Expectations', description: 'We are eager to go the extra mile to show our gratitude to employees and clients.' }
  ];

  return (
    <div className="pt-16">

      {/* Hero Section */}
      <section id="hero" className="relative bg-gradient-to-br from-[#2254D2] to-[#1a3ca8] text-gray-900 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <IRSLogoTitle />
              <p className="text-lg sm:text-xl text-white/80 mb-4 mt-6">
                Established in 2015 to support international and domestic governments as well as private sector training organizations.
              </p>
              <p className="text-lg sm:text-xl text-white/80 mb-8">
                International Responder is uniquely prepared to support these organizations.
              </p>
              <div className="flex flex-wrap gap-4">
                {stats.map((stat, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm p-4 rounded-lg border border-white/20">
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                    <p className="text-white/80 text-sm">{stat.label}</p>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex justify-center">
              <img
                src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                alt="Team collaboration"
                className="rounded-xl shadow-2xl w-full max-w-md transform transition-transform hover:scale-[1.02]"
              />
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-white" style={{ clipPath: 'polygon(0 100%, 100% 100%, 100% 0)' }}></div>
      </section>

      {/* Belief Section */}
      <section id="believe" className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Believe}
                alt="Teamwork"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Philosophy</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">We Believe</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  in the power of teamwork. With a supportive and encouraging nexus, each project set for success. Our employees rely on us and we trust them. With the company on the cutting edge, we have a passion and desire to ensure successes and time efficient outcomes for our partners and stakeholders. We inspire straightforward and personable service.
                </p>
              </div>
              <Link
                to={'/contact'}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors group"
              >
                Work With Us
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Envision Section */}
      <section id="envision" className="py-20 bg-gray-50">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row-reverse items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Envision}
                alt="Vision"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Future</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">We Envision</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  innovative sciences and technologies effectively integrating into the design, construction, operation, and maintenance of our clients projects; promote stewardship and sustainability in all phases of our work product and employment environment. We envision a mutually beneficial connection with our clients and employees based on trust, ethics, and value driven service.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Experts Section */}
      <section id="experts" className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Accompolishments}
                alt="Experts"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Team</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Accomplished Experts</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  International Responder is uniquely prepared to support any organization for any situation. We are subject matter experts (SMEs) for emergency response and public health with over 50 years of combined experience.
                </p>
                <Link
                  to="https://www.linkedin.com/posts/james-mullikin-42a30211_we-had-a-wonderful-kickoff-with-the-insight-activity-7129517543630761987-_31L/?trk=public_profile_share_view"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-4 inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                >
                  Read about our recent trip to DC
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 bg-gray-50">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row-reverse items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Portfolio}
                alt="Portfolio"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Work</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Diverse Portfolio</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  We separate ourselves from other firms with the diversity of our clients, projects, and professional expertise. Our successful growth is a result of our ability to offer our services at roughly half the cost of our competitors.
                </p>
              </div>
              <Link
                to={'/solutions'}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors group"
              >
                Explore Our Services
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Clients Section */}
      <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center gap-10">
            <div className="flex-shrink-0 w-full md:w-[45%]">
              <img
                src={Clients}
                alt="Clients"
                className="rounded-xl shadow-lg w-full object-cover transform transition-transform hover:scale-[1.02]"
              />
            </div>
            <div className="w-full md:w-[55%]">
              <div className='mb-8'>
                <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Clients</span>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Organizations</h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  Trained & Prepared by IRS. Training and Exercises prepare you for a wide variety of emergencies and give you the tools to respond proactively. Each one of these clients are ready for whatever disaster strikes next.
                </p>
              </div>
              <Link
                to={'/contact'}
                className="inline-flex items-center px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors group"
              >
                Work With us
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section id="values" className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="inline-block px-3 py-1 text-sm font-semibold text-blue-600 bg-blue-100 rounded-full mb-4">Our Foundation</span>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">Driving innovation in healthcare emergency response through our unwavering commitment to excellence</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-gray-100"
              >
                <div className="w-[100%] mb-6">
                  <img className='w-[100%] h-60' src={value.image} alt={value.title} />
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-900">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-24 items-center">
            <div>
              <h2 className="text-3xl sm:text-4xl font-bold mb-6 text-gray-900">Accomplishments</h2>
              <p className="text-gray-600 mb-6 text-lg">
                Contracted by DC Government to instruct HSEEP Compliant Full Scale and Table Top Exercises, Weaponized Anthrax Full Scale Exercise, After Action Reporting, Data Analytics, Mass Trauma Planning and National Special Security Events Strategic Support
              </p>
              <p className="text-gray-600 mb-6 text-lg">
                Contracted by FEMA  to program executive leadership education as well as training academy logistics and student services
              </p>
              <p className="text-gray-600 mb-6 text-lg" >Developed Virtual Instructor led E-Learning Training Modules for Leidos, synchronous and asynchronous. Provided subject matter experts and training instructors for response necessities including but not limited to CBRNE Training, Healthcare Emergency Response, law enforcement, hazardous materials and live agents. Infrastructure support, developing audio/visual studios</p>
              <p className="text-gray-600 mb-6 text-lg" >Contracted by eHealth Africa for NIMS and ICS Training. Logistics Coordination for Ebola response as well as inter-agency coordination for disaster support</p>
              <p className="text-gray-600 mb-6 text-lg" >Provided Public Health and Healthcare System Disaster Response Support for Puerto Rico Department of Health.</p>
            </div>
            <div className="flex flex-col space-y-6 justify-center">
              <img
                src={Acc1}
                alt="Office meeting"
                className="rounded-xl shadow-2xl w-full max-w-md transform transition-transform hover:scale-[1.02]"
              />
              <img
                src={Acc2}
                alt="Office meeting"
                className="rounded-xl shadow-2xl w-full max-w-md transform transition-transform hover:scale-[1.02]"
              />
            </div>
          </div>
        </div>
      </section>
      <section className='py-20 bg-white' >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 text-center mb-8">Alliances</h2>
          <div className='flex flex-wrap md:flex-row  justify-around items-center'>
            <img src={All1} alt="Alliance1" />
            <img src={All2} alt="Alliance2" />
            <img src={All3} alt="Alliance3" />
            <img src={All4} alt="Alliance4" />
            <img src={All5} alt="Alliance5" />
          </div>
        </div>
      </section>

      <section className='py-20 bg-[#F9FAFB] mb-10'>
        <div className=" max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center px-4 sm:px-6 lg:px-8">
          <div>
            <h2 className='text-3xl sm:text-4xl font-bold text-gray-900 text-center mb-8'>Additional Info</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <span className="font-semibold text-gray-700">EIN</span>
                <p className="text-gray-900">47-4010088</p>
              </div>
              <div className="space-y-1">
                <span className="font-semibold text-gray-700">DUNS</span>
                <p className="text-gray-900">079943388</p>
              </div>
              <div className="space-y-1">
                <span className="font-semibold text-gray-700">CAGE</span>
                <p className="text-gray-900">7YTM4</p>
              </div>
              <div className="space-y-1">
                <span className="font-semibold text-gray-700">Licensed to Operate In</span>
                <p className="text-gray-900">
                  <span className="block">Alabama</span>
                  <span className="block">Washington D.C</span>
                  <span className="block">Maryland</span>
                  <span className="block">West Virginia</span>
                </p>
              </div>
            </div>
          </div>
          <div>
            <h2 className='text-3xl sm:text-4xl font-bold text-gray-900 text-center mb-8'>Certifications</h2>
            <img src={Certif1} alt="" />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-6">Ready to Transform Your Emergency Response?</h2>
          <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Partner with International Responder Systems and experience the difference of working with true experts in public health and emergency response.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              to="/contact"
              className="px-8 py-3 bg-white text-blue-600 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              Get in Touch
            </Link>
            <Link
              to="/solutions"
              className="px-8 py-3 border border-white text-white rounded-lg font-medium hover:bg-white/10 transition-colors"
            >
              Explore Solutions
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}